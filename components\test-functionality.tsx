"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { videoStore, type UploadedVideo } from "@/lib/video-store"
import { VideoUploadDialog } from "@/components/video-upload-dialog"
import { useToast } from "@/hooks/use-toast"
import { Upload, Play, Download, Trash2, CheckCircle, XCircle } from "lucide-react"

export function TestFunctionality() {
  const [videos, setVideos] = useState<UploadedVideo[]>([])
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const [analyzing, setAnalyzing] = useState<string | null>(null)
  const [testResults, setTestResults] = useState<Record<string, boolean>>({})
  const { toast } = useToast()

  useEffect(() => {
    const updateVideos = () => {
      setVideos(videoStore.getAllVideos())
    }

    updateVideos()
    const unsubscribe = videoStore.subscribe(updateVideos)
    return unsubscribe
  }, [])

  const handleVideoUploaded = (video: UploadedVideo) => {
    videoStore.addVideo(video)
    setTestResults(prev => ({ ...prev, upload: true }))
    toast({
      title: "✅ Upload Test Passed",
      description: `${video.name} uploaded successfully`,
    })
  }

  const handleAnalyze = async (videoId: string) => {
    setAnalyzing(videoId)
    try {
      await videoStore.analyzeVideo(videoId)
      setTestResults(prev => ({ ...prev, analysis: true }))
      toast({
        title: "✅ Analysis Test Passed",
        description: "Video analysis completed successfully",
      })
    } catch (error) {
      setTestResults(prev => ({ ...prev, analysis: false }))
      toast({
        title: "❌ Analysis Test Failed",
        description: "Video analysis failed",
        variant: "destructive",
      })
    } finally {
      setAnalyzing(null)
    }
  }

  const handleExport = () => {
    try {
      const csvData = videoStore.exportResults('csv')
      const blob = new Blob([csvData], { type: 'text/csv' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `test_export_${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      setTestResults(prev => ({ ...prev, export: true }))
      toast({
        title: "✅ Export Test Passed",
        description: "Data exported successfully",
      })
    } catch (error) {
      setTestResults(prev => ({ ...prev, export: false }))
      toast({
        title: "❌ Export Test Failed",
        description: "Export failed",
        variant: "destructive",
      })
    }
  }

  const handleDelete = (videoId: string) => {
    videoStore.removeVideo(videoId)
    setTestResults(prev => ({ ...prev, delete: true }))
    toast({
      title: "✅ Delete Test Passed",
      description: "Video deleted successfully",
    })
  }

  const runAllTests = () => {
    setTestResults({})
    toast({
      title: "🧪 Running Tests",
      description: "Please test each functionality manually",
    })
  }

  const clearAllData = () => {
    videoStore.cleanup()
    setTestResults({})
    toast({
      title: "🗑️ Data Cleared",
      description: "All test data has been cleared",
    })
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const TestStatus = ({ test, label }: { test: string, label: string }) => (
    <div className="flex items-center gap-2">
      {testResults[test] === true && <CheckCircle className="h-4 w-4 text-green-500" />}
      {testResults[test] === false && <XCircle className="h-4 w-4 text-red-500" />}
      {testResults[test] === undefined && <div className="h-4 w-4 rounded-full bg-gray-300" />}
      <span className="text-sm">{label}</span>
    </div>
  )

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🧪 Functionality Test Suite</CardTitle>
          <CardDescription>
            Test all major features of the traffic video analysis application
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <TestStatus test="upload" label="Video Upload" />
            <TestStatus test="analysis" label="Video Analysis" />
            <TestStatus test="export" label="Data Export" />
            <TestStatus test="delete" label="Video Delete" />
          </div>
          
          <div className="flex gap-2">
            <Button onClick={runAllTests} variant="outline">
              🧪 Start Tests
            </Button>
            <Button onClick={clearAllData} variant="destructive">
              🗑️ Clear Data
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>📤 Upload Test</CardTitle>
          <CardDescription>Test video upload functionality</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => setUploadDialogOpen(true)}>
            <Upload className="mr-2 h-4 w-4" />
            Test Upload Video
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>📊 Video Library ({videos.length} videos)</CardTitle>
          <CardDescription>Manage and test uploaded videos</CardDescription>
        </CardHeader>
        <CardContent>
          {videos.length === 0 ? (
            <p className="text-muted-foreground">No videos uploaded yet. Upload a video to test functionality.</p>
          ) : (
            <div className="space-y-3">
              {videos.map((video) => (
                <div key={video.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex-1">
                    <div className="font-medium">{video.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {formatFileSize(video.size)} • {formatDuration(video.duration)} • 
                      {video.analyzed ? (
                        <Badge variant="outline" className="ml-2">Analyzed</Badge>
                      ) : (
                        <Badge variant="secondary" className="ml-2">Uploaded</Badge>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    {!video.analyzed && (
                      <Button 
                        size="sm" 
                        onClick={() => handleAnalyze(video.id)}
                        disabled={analyzing === video.id}
                      >
                        <Play className="h-4 w-4 mr-1" />
                        {analyzing === video.id ? 'Analyzing...' : 'Analyze'}
                      </Button>
                    )}
                    <Button size="sm" variant="outline" onClick={handleExport}>
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button size="sm" variant="destructive" onClick={() => handleDelete(video.id)}>
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <VideoUploadDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        onVideoUploaded={handleVideoUploaded}
      />
    </div>
  )
}
