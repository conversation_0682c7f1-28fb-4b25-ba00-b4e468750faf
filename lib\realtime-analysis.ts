"use client"

export interface RealtimeVehicle {
  id: string
  type: 'car' | 'truck' | 'bus' | 'motorcycle'
  speed: number
  timestamp: number
  x: number
  y: number
  width: number
  height: number
  confidence: number
  direction: 'left' | 'right' | 'up' | 'down'
  lane: number
  color: string
  isViolation: boolean
  speedLimit: number
  trackingId: string
  firstSeen: number
  lastSeen: number
  distanceTraveled: number
  averageSpeed: number
  maxSpeed: number
  minSpeed: number
}

export interface RealtimeStats {
  totalVehicles: number
  currentVehicles: number
  speedViolations: number
  averageSpeed: number
  vehicleTypes: Record<string, number>
  laneOccupancy: Record<number, number>
  trafficFlow: 'low' | 'medium' | 'high' | 'congested'
  congestionLevel: number // 0-100
  lastUpdate: Date
}

export interface RealtimeAlert {
  id: string
  type: 'speed_violation' | 'congestion' | 'accident_risk' | 'lane_violation'
  severity: 'low' | 'medium' | 'high' | 'critical'
  message: string
  timestamp: Date
  vehicleId?: string
  location?: { x: number; y: number }
  resolved: boolean
}

class RealtimeAnalysisService {
  private vehicles: Map<string, RealtimeVehicle> = new Map()
  private stats: RealtimeStats = {
    totalVehicles: 0,
    currentVehicles: 0,
    speedViolations: 0,
    averageSpeed: 0,
    vehicleTypes: {},
    laneOccupancy: {},
    trafficFlow: 'low',
    congestionLevel: 0,
    lastUpdate: new Date()
  }
  private alerts: RealtimeAlert[] = []
  private listeners: ((stats: RealtimeStats) => void)[] = []
  private alertListeners: ((alerts: RealtimeAlert[]) => void)[] = []
  private isRunning = false
  private animationId: number | null = null
  private speedLimit = 60 // km/h
  private lanes = 3

  constructor() {
    this.initializeLanes()
  }

  private initializeLanes() {
    for (let i = 0; i < this.lanes; i++) {
      this.stats.laneOccupancy[i] = 0
    }
  }

  subscribe(listener: (stats: RealtimeStats) => void) {
    this.listeners.push(listener)
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener)
    }
  }

  subscribeToAlerts(listener: (alerts: RealtimeAlert[]) => void) {
    this.alertListeners.push(listener)
    return () => {
      this.alertListeners = this.alertListeners.filter(l => l !== listener)
    }
  }

  startAnalysis() {
    if (this.isRunning) return
    this.isRunning = true
    this.animate()
  }

  stopAnalysis() {
    this.isRunning = false
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }

  private animate = () => {
    if (!this.isRunning) return

    const timestamp = Date.now()
    this.updateVehicles(timestamp)
    this.updateStats()
    this.checkForAlerts()
    this.notifyListeners()

    this.animationId = requestAnimationFrame(this.animate)
  }

  private updateVehicles(timestamp: number) {
    const currentVehicles: RealtimeVehicle[] = []
    const speedLimit = this.speedLimit

    // Generate new vehicles or update existing ones
    for (let i = 0; i < 8; i++) {
      const vehicleId = `vehicle-${i}`
      const trackingId = `track-${i}`
      
      let vehicle = this.vehicles.get(vehicleId)
      
      if (!vehicle) {
        // Create new vehicle
        const type = ['car', 'truck', 'bus', 'motorcycle'][i % 4] as RealtimeVehicle['type']
        const lane = i % this.lanes
        const direction = i % 2 === 0 ? 'right' : 'left'
        
        vehicle = {
          id: vehicleId,
          type,
          speed: 30 + Math.random() * 40,
          timestamp,
          x: direction === 'right' ? -100 : 900,
          y: 100 + lane * 70,
          width: type === 'truck' || type === 'bus' ? 100 : 80,
          height: type === 'truck' || type === 'bus' ? 50 : 40,
          confidence: 0.8 + Math.random() * 0.2,
          direction,
          lane,
          color: '#22c55e',
          isViolation: false,
          speedLimit,
          trackingId,
          firstSeen: timestamp,
          lastSeen: timestamp,
          distanceTraveled: 0,
          averageSpeed: 0,
          maxSpeed: 0,
          minSpeed: 0
        }
        this.vehicles.set(vehicleId, vehicle)
      }

      // Update vehicle position and speed
      const timeDelta = (timestamp - vehicle.lastSeen) / 1000 // seconds
      const pixelsPerSecond = vehicle.speed * 0.3
      const distance = pixelsPerSecond * timeDelta

      if (vehicle.direction === 'right') {
        vehicle.x += distance
        if (vehicle.x > 900) {
          // Vehicle left the scene, remove it
          this.vehicles.delete(vehicleId)
          continue
        }
      } else {
        vehicle.x -= distance
        if (vehicle.x < -100) {
          // Vehicle left the scene, remove it
          this.vehicles.delete(vehicleId)
          continue
        }
      }

      // Update speed with realistic variations
      const speedVariation = (Math.random() - 0.5) * 10
      vehicle.speed = Math.max(15, Math.min(80, vehicle.speed + speedVariation))
      
      // Update tracking data
      vehicle.distanceTraveled += distance
      vehicle.lastSeen = timestamp
      vehicle.timestamp = timestamp
      vehicle.averageSpeed = vehicle.distanceTraveled / ((timestamp - vehicle.firstSeen) / 1000)
      vehicle.maxSpeed = Math.max(vehicle.maxSpeed, vehicle.speed)
      vehicle.minSpeed = vehicle.minSpeed === 0 ? vehicle.speed : Math.min(vehicle.minSpeed, vehicle.speed)

      // Check for speed violations
      vehicle.isViolation = vehicle.speed > speedLimit
      vehicle.color = vehicle.isViolation ? '#ef4444' : 
                     vehicle.speed > speedLimit * 0.8 ? '#f59e0b' : '#22c55e'

      // Only include vehicles that are visible
      if (vehicle.x > -vehicle.width && vehicle.x < 900) {
        currentVehicles.push(vehicle)
      }
    }

    // Update current vehicles count
    this.stats.currentVehicles = currentVehicles.length
  }

  private updateStats() {
    const vehicles = Array.from(this.vehicles.values())
    
    if (vehicles.length === 0) {
      const emptyLanes: Record<number, number> = {}
      for (let i = 0; i < this.lanes; i++) {
        emptyLanes[i] = 0
      }
      
      this.stats = {
        ...this.stats,
        currentVehicles: 0,
        speedViolations: 0,
        averageSpeed: 0,
        vehicleTypes: {},
        laneOccupancy: emptyLanes,
        trafficFlow: 'low',
        congestionLevel: 0,
        lastUpdate: new Date()
      }
      return
    }

    // Calculate vehicle type distribution
    const vehicleTypes: Record<string, number> = {}
    vehicles.forEach(v => {
      vehicleTypes[v.type] = (vehicleTypes[v.type] || 0) + 1
    })

    // Calculate lane occupancy
    const laneOccupancy: Record<number, number> = {}
    for (let i = 0; i < this.lanes; i++) {
      laneOccupancy[i] = vehicles.filter(v => v.lane === i).length
    }

    // Calculate average speed and violations
    const totalSpeed = vehicles.reduce((sum, v) => sum + v.speed, 0)
    const averageSpeed = totalSpeed / vehicles.length
    const speedViolations = vehicles.filter(v => v.isViolation).length

    // Determine traffic flow and congestion level
    const congestionLevel = Math.min(100, (vehicles.length / 8) * 100)
    let trafficFlow: 'low' | 'medium' | 'high' | 'congested'
    
    if (congestionLevel < 25) trafficFlow = 'low'
    else if (congestionLevel < 50) trafficFlow = 'medium'
    else if (congestionLevel < 75) trafficFlow = 'high'
    else trafficFlow = 'congested'

    this.stats = {
      totalVehicles: this.stats.totalVehicles + vehicles.length,
      currentVehicles: vehicles.length,
      speedViolations,
      averageSpeed: Math.round(averageSpeed * 10) / 10,
      vehicleTypes,
      laneOccupancy,
      trafficFlow,
      congestionLevel: Math.round(congestionLevel),
      lastUpdate: new Date()
    }
  }

  private checkForAlerts() {
    const vehicles = Array.from(this.vehicles.values())
    
    // Check for speed violations
    vehicles.forEach(vehicle => {
      if (vehicle.isViolation && vehicle.speed > this.speedLimit * 1.2) {
        this.addAlert({
          type: 'speed_violation',
          severity: vehicle.speed > this.speedLimit * 1.5 ? 'critical' : 'high',
          message: `${vehicle.type} exceeding speed limit by ${Math.round(vehicle.speed - this.speedLimit)} km/h`,
          vehicleId: vehicle.id,
          location: { x: vehicle.x, y: vehicle.y }
        })
      }
    })

    // Check for congestion
    if (this.stats.congestionLevel > 80) {
      this.addAlert({
        type: 'congestion',
        severity: 'medium',
        message: `High traffic congestion detected (${this.stats.congestionLevel}%)`
      })
    }

    // Check for lane violations (vehicles changing lanes frequently)
    vehicles.forEach(vehicle => {
      // This would be implemented with more sophisticated tracking
      // For now, we'll simulate occasional lane violations
      if (Math.random() < 0.01) { // 1% chance per update
        this.addAlert({
          type: 'lane_violation',
          severity: 'low',
          message: `${vehicle.type} detected in improper lane`,
          vehicleId: vehicle.id,
          location: { x: vehicle.x, y: vehicle.y }
        })
      }
    })

    // Clean up old alerts (older than 5 minutes)
    const fiveMinutesAgo = Date.now() - 5 * 60 * 1000
    this.alerts = this.alerts.filter(alert => 
      alert.timestamp.getTime() > fiveMinutesAgo || !alert.resolved
    )
  }

  private addAlert(alertData: Omit<RealtimeAlert, 'id' | 'timestamp' | 'resolved'>) {
    const alert: RealtimeAlert = {
      ...alertData,
      id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date(),
      resolved: false
    }
    
    this.alerts.unshift(alert)
    this.notifyAlertListeners()
  }

  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.stats))
  }

  private notifyAlertListeners() {
    this.alertListeners.forEach(listener => listener([...this.alerts]))
  }

  getCurrentStats(): RealtimeStats {
    return { ...this.stats }
  }

  getCurrentVehicles(): RealtimeVehicle[] {
    return Array.from(this.vehicles.values())
  }

  getAlerts(): RealtimeAlert[] {
    return [...this.alerts]
  }

  setSpeedLimit(limit: number) {
    this.speedLimit = limit
    // Update existing vehicles
    this.vehicles.forEach(vehicle => {
      vehicle.speedLimit = limit
      vehicle.isViolation = vehicle.speed > limit
    })
  }

  resolveAlert(alertId: string) {
    const alert = this.alerts.find(a => a.id === alertId)
    if (alert) {
      alert.resolved = true
      this.notifyAlertListeners()
    }
  }

  clearAlerts() {
    this.alerts = []
    this.notifyAlertListeners()
  }
}

// Create singleton instance
export const realtimeAnalysis = new RealtimeAnalysisService() 