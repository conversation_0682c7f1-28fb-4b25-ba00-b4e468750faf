"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Download, Share2 } from "lucide-react"
import { videoStore, type UploadedVideo } from "@/lib/video-store"
import { useSearchParams } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

export function VideoAnalysisHeader() {
  const [video, setVideo] = useState<UploadedVideo | null>(null)
  const searchParams = useSearchParams()
  const { toast } = useToast()

  const videoId = searchParams ? searchParams.get('id') : null

  useEffect(() => {
    if (videoId) {
      const foundVideo = videoStore.getVideo(videoId)
      setVideo(foundVideo || null)
    }
  }, [videoId])

  const handleExport = () => {
    if (!video?.analyzed) {
      toast({
        title: "No analysis data",
        description: "Please analyze the video first before exporting.",
        variant: "destructive",
      })
      return
    }

    try {
      const csvData = videoStore.exportResults('csv')
      const blob = new Blob([csvData], { type: 'text/csv' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${video.name.replace(/\.[^/.]+$/, "")}_analysis.csv`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: "Export successful",
        description: "Analysis results have been exported to CSV.",
      })
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Failed to export analysis results.",
        variant: "destructive",
      })
    }
  }

  const handleShare = () => {
    if (!video) return

    const shareData = {
      title: `Traffic Analysis - ${video.name}`,
      text: `Check out this traffic analysis for ${video.name}`,
      url: window.location.href,
    }

    if (navigator.share) {
      navigator.share(shareData).catch(() => {
        // Fallback to clipboard
        navigator.clipboard.writeText(window.location.href)
        toast({
          title: "Link copied",
          description: "Analysis link has been copied to clipboard.",
        })
      })
    } else {
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Link copied",
        description: "Analysis link has been copied to clipboard.",
      })
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Video Analysis</h1>
        <p className="text-muted-foreground">
          {video ? (
            `${video.name} • ${formatDuration(video.duration)} • ${formatDate(video.uploadedAt)}`
          ) : (
            "No video selected"
          )}
        </p>
      </div>
      <div className="flex gap-2">
        <Button variant="outline" onClick={handleExport} disabled={!video?.analyzed}>
          <Download className="mr-2 h-4 w-4" />
          Export
        </Button>
        <Button variant="outline" onClick={handleShare} disabled={!video}>
          <Share2 className="mr-2 h-4 w-4" />
          Share
        </Button>
      </div>
    </div>
  )
}
