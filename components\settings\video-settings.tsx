"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"

export function VideoSettings() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Video Processing Settings</CardTitle>
        <CardDescription>Configure video processing and analysis options</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="frame-rate">Frame Sampling Rate</Label>
            <Select defaultValue="5">
              <SelectTrigger id="frame-rate">
                <SelectValue placeholder="Select frame rate" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Every frame</SelectItem>
                <SelectItem value="2">Every 2 frames</SelectItem>
                <SelectItem value="5">Every 5 frames</SelectItem>
                <SelectItem value="10">Every 10 frames</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">Processing fewer frames reduces computation time</p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="detection-model">Detection Model</Label>
            <Select defaultValue="yolov8">
              <SelectTrigger id="detection-model">
                <SelectValue placeholder="Select detection model" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="yolov8">YOLOv8</SelectItem>
                <SelectItem value="faster-rcnn">Faster R-CNN</SelectItem>
                <SelectItem value="ssd">SSD</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              Different models offer trade-offs between speed and accuracy
            </p>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="storage-limit">Video Storage Limit (days)</Label>
          <Input id="storage-limit" type="number" defaultValue={30} />
          <p className="text-xs text-muted-foreground">Videos older than this will be automatically deleted</p>
        </div>

        <div className="flex items-center space-x-2">
          <Switch id="gpu-acceleration" defaultChecked />
          <Label htmlFor="gpu-acceleration">Enable GPU acceleration when available</Label>
        </div>

        <Button>Save Video Settings</Button>
      </CardContent>
    </Card>
  )
}
