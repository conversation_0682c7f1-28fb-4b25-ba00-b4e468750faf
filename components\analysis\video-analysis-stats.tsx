import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { Car, AlertTriangle, ArrowDown } from "lucide-react"

interface VideoAnalysisStatsProps {
  className?: string
}

export function VideoAnalysisStats({ className }: VideoAnalysisStatsProps) {
  const stats = [
    {
      title: "Total Vehicles",
      value: "142",
      icon: <Car className="h-4 w-4 text-muted-foreground" />,
    },
    {
      title: "High Speed Vehicles",
      value: "12",
      icon: <AlertTriangle className="h-4 w-4 text-red-500" />,
    },
    {
      title: "Low Speed Vehicles",
      value: "5",
      icon: <ArrowDown className="h-4 w-4 text-blue-500" />,
    },
  ]

  const vehicleTypes = [
    { type: "Cars", count: 98 },
    { type: "Trucks", count: 24 },
    { type: "Buses", count: 12 },
    { type: "Motorcycles", count: 8 },
  ]

  return (
    <Card className={cn(className)}>
      <CardHeader>
        <CardTitle>Analysis Results</CardTitle>
        <CardDescription>Statistics for the current video</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-3 gap-4">
          {stats.map((stat, index) => (
            <div key={index} className="flex flex-col items-center justify-center p-2 bg-muted rounded-md">
              <div className="flex items-center justify-center mb-1">{stat.icon}</div>
              <div className="text-2xl font-bold">{stat.value}</div>
              <div className="text-xs text-muted-foreground text-center">{stat.title}</div>
            </div>
          ))}
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">Vehicle Types</h4>
          <div className="space-y-2">
            {vehicleTypes.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <span className="text-sm">{item.type}</span>
                <span className="text-sm font-medium">{item.count}</span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
