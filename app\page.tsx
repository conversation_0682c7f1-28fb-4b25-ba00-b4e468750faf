import { DashboardHeader } from "@/components/dashboard/dashboard-header"
import { DashboardStats } from "@/components/dashboard/dashboard-stats"
import { RecentUploads } from "@/components/dashboard/recent-uploads"
import { VehicleTypeChart } from "@/components/dashboard/vehicle-type-chart"
import { SpeedDistributionChart } from "@/components/dashboard/speed-distribution-chart"

export default function DashboardPage() {
  return (
    <div className="container mx-auto p-6">
      <DashboardHeader />
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mt-6">
        <DashboardStats />
      </div>
      <div className="grid gap-6 md:grid-cols-2 mt-6">
        <VehicleTypeChart />
        <SpeedDistributionChart />
      </div>
      <div className="mt-6">
        <RecentUploads />
      </div>
    </div>
  )
}
