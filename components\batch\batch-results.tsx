import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Eye } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface BatchResultsProps {
  className?: string
}

export function BatchResults({ className }: BatchResultsProps) {
  const batchData = [
    {
      id: "1",
      location: "Highway 101",
      date: "2023-06-12",
      videos: 5,
      totalVehicles: 742,
      avgSpeed: 68,
      highSpeed: 52,
      lowSpeed: 23,
    },
    {
      id: "2",
      location: "Downtown",
      date: "2023-06-11",
      videos: 3,
      totalVehicles: 523,
      avgSpeed: 42,
      highSpeed: 18,
      lowSpeed: 45,
    },
    {
      id: "3",
      location: "School Zone",
      date: "2023-06-10",
      videos: 2,
      totalVehicles: 312,
      avgSpeed: 35,
      highSpeed: 28,
      lowSpeed: 12,
    },
    {
      id: "4",
      location: "Bridge",
      date: "2023-06-09",
      videos: 4,
      totalVehicles: 628,
      avgSpeed: 55,
      highSpeed: 42,
      lowSpeed: 18,
    },
    {
      id: "5",
      location: "Intersection",
      date: "2023-06-08",
      videos: 3,
      totalVehicles: 486,
      avgSpeed: 38,
      highSpeed: 24,
      lowSpeed: 32,
    },
  ]

  return (
    <Card className={cn(className)}>
      <CardHeader>
        <CardTitle>Batch Analysis Results</CardTitle>
        <CardDescription>Aggregated results by location and date</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Location</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Videos</TableHead>
              <TableHead>Total Vehicles</TableHead>
              <TableHead>Avg. Speed</TableHead>
              <TableHead>High Speed</TableHead>
              <TableHead>Low Speed</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {batchData.map((batch) => (
              <TableRow key={batch.id}>
                <TableCell className="font-medium">{batch.location}</TableCell>
                <TableCell>{batch.date}</TableCell>
                <TableCell>{batch.videos}</TableCell>
                <TableCell>{batch.totalVehicles}</TableCell>
                <TableCell>{batch.avgSpeed} km/h</TableCell>
                <TableCell>
                  <Badge variant="destructive">{batch.highSpeed}</Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="secondary">{batch.lowSpeed}</Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={`/batch/${batch.id}`}>
                      <Eye className="h-4 w-4 mr-2" />
                      Details
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
