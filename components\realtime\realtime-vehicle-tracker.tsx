"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { 
  Car, 
  Truck, 
  Bus, 
  Bike, 
  Eye, 
  EyeOff, 
  Maximize2,
  Minimize2,
  Settings
} from "lucide-react"
import { realtimeAnalysis, type RealtimeVehicle } from "@/lib/realtime-analysis"

export function RealtimeVehicleTracker() {
  const [vehicles, setVehicles] = useState<RealtimeVehicle[]>([])
  const [showSpeedLabels, setShowSpeedLabels] = useState(true)
  const [showTrackingLines, setShowTrackingLines] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>(0)

  useEffect(() => {
    const animate = () => {
      const currentVehicles = realtimeAnalysis.getCurrentVehicles()
      setVehicles(currentVehicles)
      drawVehicles(currentVehicles)
      animationRef.current = requestAnimationFrame(animate)
    }

    animationRef.current = requestAnimationFrame(animate)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [])

  const drawVehicles = (currentVehicles: RealtimeVehicle[]) => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Draw road background
    drawRoad(ctx, canvas.width, canvas.height)

    // Draw vehicles
    currentVehicles.forEach(vehicle => {
      drawVehicle(ctx, vehicle)
    })

    // Draw tracking lines if enabled
    if (showTrackingLines) {
      drawTrackingLines(ctx, currentVehicles)
    }
  }

  const drawRoad = (ctx: CanvasRenderingContext2D, width: number, height: number) => {
    // Road background
    ctx.fillStyle = '#374151'
    ctx.fillRect(0, 80, width, 220)

    // Lane dividers
    ctx.strokeStyle = '#ffffff'
    ctx.setLineDash([20, 20])
    ctx.lineWidth = 2

    for (let i = 1; i < 3; i++) {
      const y = 80 + (i * 70)
      ctx.beginPath()
      ctx.moveTo(0, y)
      ctx.lineTo(width, y)
      ctx.stroke()
    }

    ctx.setLineDash([])
  }

  const drawVehicle = (ctx: CanvasRenderingContext2D, vehicle: RealtimeVehicle) => {
    const { x, y, width, height, speed, type, color, isViolation, direction } = vehicle

    // Vehicle body
    ctx.fillStyle = color
    ctx.fillRect(x, y, width, height)

    // Vehicle border
    ctx.strokeStyle = isViolation ? '#ef4444' : '#ffffff'
    ctx.lineWidth = 2
    ctx.strokeRect(x, y, width, height)

    // Vehicle icon
    const iconSize = 16
    const iconX = x + width / 2 - iconSize / 2
    const iconY = y + height / 2 - iconSize / 2

    ctx.fillStyle = '#ffffff'
    ctx.font = `${iconSize}px Arial`
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'

    const icon = getVehicleIcon(type)
    ctx.fillText(icon, iconX + iconSize / 2, iconY + iconSize / 2)

    // Speed label
    if (showSpeedLabels) {
      ctx.fillStyle = '#ffffff'
      ctx.font = '12px Arial'
      ctx.textAlign = 'center'
      ctx.fillText(`${Math.round(speed)} km/h`, x + width / 2, y - 5)

      // Speed violation indicator
      if (isViolation) {
        ctx.fillStyle = '#ef4444'
        ctx.fillText('VIOLATION', x + width / 2, y - 20)
      }
    }

    // Direction indicator
    const arrowSize = 8
    const arrowX = direction === 'right' ? x + width + 5 : x - 10
    const arrowY = y + height / 2

    ctx.fillStyle = '#ffffff'
    ctx.beginPath()
    if (direction === 'right') {
      ctx.moveTo(arrowX, arrowY)
      ctx.lineTo(arrowX + arrowSize, arrowY - arrowSize / 2)
      ctx.lineTo(arrowX + arrowSize, arrowY + arrowSize / 2)
    } else {
      ctx.moveTo(arrowX, arrowY)
      ctx.lineTo(arrowX - arrowSize, arrowY - arrowSize / 2)
      ctx.lineTo(arrowX - arrowSize, arrowY + arrowSize / 2)
    }
    ctx.closePath()
    ctx.fill()
  }

  const drawTrackingLines = (ctx: CanvasRenderingContext2D, currentVehicles: RealtimeVehicle[]) => {
    ctx.strokeStyle = 'rgba(59, 130, 246, 0.3)'
    ctx.lineWidth = 1
    ctx.setLineDash([5, 5])

    currentVehicles.forEach(vehicle => {
      // Draw tracking line from previous position to current position
      const timeDelta = (Date.now() - vehicle.lastSeen) / 1000
      const pixelsPerSecond = vehicle.speed * 0.3
      const distance = pixelsPerSecond * timeDelta

      let prevX = vehicle.x
      if (vehicle.direction === 'right') {
        prevX = vehicle.x - distance
      } else {
        prevX = vehicle.x + distance
      }

      ctx.beginPath()
      ctx.moveTo(prevX, vehicle.y + vehicle.height / 2)
      ctx.lineTo(vehicle.x, vehicle.y + vehicle.height / 2)
      ctx.stroke()
    })

    ctx.setLineDash([])
  }

  const getVehicleIcon = (type: string): string => {
    switch (type) {
      case 'car': return '🚗'
      case 'truck': return '🚛'
      case 'bus': return '🚌'
      case 'motorcycle': return '🏍️'
      default: return '🚗'
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  return (
    <Card className={isFullscreen ? "fixed inset-0 z-50 m-0 rounded-none" : ""}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Car className="h-5 w-5" />
              Real-time Vehicle Tracker
            </CardTitle>
            <CardDescription>
              Live vehicle tracking and movement visualization
            </CardDescription>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="speed-labels"
                checked={showSpeedLabels}
                onCheckedChange={setShowSpeedLabels}
              />
              <Label htmlFor="speed-labels" className="text-sm">
                Speed Labels
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="tracking-lines"
                checked={showTrackingLines}
                onCheckedChange={setShowTrackingLines}
              />
              <Label htmlFor="tracking-lines" className="text-sm">
                Tracking Lines
              </Label>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFullscreen}
            >
              {isFullscreen ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="relative">
          <canvas
            ref={canvasRef}
            width={800}
            height={400}
            className="w-full border rounded-lg bg-gray-900"
          />
          
          {/* Vehicle count overlay */}
          <div className="absolute top-4 left-4 bg-black/50 text-white px-3 py-1 rounded">
            <span className="text-sm font-medium">
              {vehicles.length} vehicles
            </span>
          </div>

          {/* Speed violations overlay */}
          <div className="absolute top-4 right-4 bg-red-500/80 text-white px-3 py-1 rounded">
            <span className="text-sm font-medium">
              {vehicles.filter(v => v.isViolation).length} violations
            </span>
          </div>

          {/* Lane indicators */}
          <div className="absolute left-4 top-16 space-y-2">
            {[0, 1, 2].map(lane => (
              <div key={lane} className="bg-black/50 text-white px-2 py-1 rounded text-xs">
                Lane {lane + 1}: {vehicles.filter(v => v.lane === lane).length}
              </div>
            ))}
          </div>
        </div>

        {/* Vehicle details */}
        <div className="mt-4 grid gap-2 md:grid-cols-2 lg:grid-cols-3">
          {vehicles.slice(0, 6).map(vehicle => (
            <div
              key={vehicle.id}
              className="flex items-center justify-between p-2 border rounded bg-gray-50"
            >
              <div className="flex items-center gap-2">
                <span className="text-lg">{getVehicleIcon(vehicle.type)}</span>
                <div>
                  <div className="text-sm font-medium capitalize">{vehicle.type}</div>
                  <div className="text-xs text-muted-foreground">
                    ID: {vehicle.trackingId}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-sm font-bold ${vehicle.isViolation ? 'text-red-600' : 'text-green-600'}`}>
                  {Math.round(vehicle.speed)} km/h
                </div>
                <Badge variant={vehicle.isViolation ? "destructive" : "secondary"} className="text-xs">
                  {vehicle.isViolation ? 'VIOLATION' : 'NORMAL'}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 