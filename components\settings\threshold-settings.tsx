"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"

export function ThresholdSettings() {
  const [highSpeedThreshold, setHighSpeedThreshold] = useState(80)
  const [lowSpeedThreshold, setLowSpeedThreshold] = useState(20)
  const [enableNotifications, setEnableNotifications] = useState(true)

  return (
    <Card>
      <CardHeader>
        <CardTitle>Speed Threshold Settings</CardTitle>
        <CardDescription>Configure default speed thresholds for all videos</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="high-speed">High Speed Threshold (km/h)</Label>
            <Input
              id="high-speed"
              type="number"
              value={highSpeedThreshold}
              onChange={(e) => setHighSpeedThreshold(Number(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">Vehicles above this speed will be marked as high speed</p>
          </div>
          <div className="space-y-2">
            <Label htmlFor="low-speed">Low Speed Threshold (km/h)</Label>
            <Input
              id="low-speed"
              type="number"
              value={lowSpeedThreshold}
              onChange={(e) => setLowSpeedThreshold(Number(e.target.value))}
            />
            <p className="text-xs text-muted-foreground">Vehicles below this speed will be marked as low speed</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Switch id="notifications" checked={enableNotifications} onCheckedChange={setEnableNotifications} />
          <Label htmlFor="notifications">Enable notifications for high speed violations</Label>
        </div>

        <Button>Save Threshold Settings</Button>
      </CardContent>
    </Card>
  )
}
