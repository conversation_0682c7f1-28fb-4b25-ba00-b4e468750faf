import { Suspense } from "react"
import { RealtimeDashboard } from "@/components/realtime/realtime-dashboard"
import { RealtimeVehicleTracker } from "@/components/realtime/realtime-vehicle-tracker"
import { RealtimeAlerts } from "@/components/realtime/realtime-alerts"
import { RealtimeAnalytics } from "@/components/realtime/realtime-analytics"

function RealtimeContent() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Real-time Traffic Analysis</h1>
          <p className="text-muted-foreground">
            Live monitoring and analysis of traffic patterns with real-time vehicle tracking
          </p>
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-6">
          <RealtimeDashboard />
          <RealtimeVehicleTracker />
        </div>
        <div>
          <RealtimeAlerts />
        </div>
      </div>

      <RealtimeAnalytics />
    </div>
  )
}

export default function RealtimePage() {
  return (
    <Suspense fallback={<div className="container mx-auto p-6">Loading real-time analysis...</div>}>
      <RealtimeContent />
    </Suspense>
  )
} 