import { <PERSON>, TableBody, Table<PERSON>ell, TableHead, <PERSON><PERSON><PERSON><PERSON>, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Eye } from "lucide-react"
import Link from "next/link"

export function RecentUploads() {
  const videos = [
    {
      id: "1",
      name: "Highway-101-North.mp4",
      date: "2023-06-12",
      duration: "10:24",
      vehicles: 142,
      highSpeed: 12,
      lowSpeed: 5,
      status: "completed",
    },
    {
      id: "2",
      name: "Downtown-Main-St.mp4",
      date: "2023-06-11",
      duration: "15:30",
      vehicles: 203,
      highSpeed: 8,
      lowSpeed: 15,
      status: "completed",
    },
    {
      id: "3",
      name: "Intersection-5th-Ave.mp4",
      date: "2023-06-10",
      duration: "08:15",
      vehicles: 98,
      highSpeed: 3,
      lowSpeed: 7,
      status: "completed",
    },
    {
      id: "4",
      name: "School-Zone-Morning.mp4",
      date: "2023-06-09",
      duration: "12:45",
      vehicles: 156,
      highSpeed: 18,
      lowSpeed: 4,
      status: "completed",
    },
    {
      id: "5",
      name: "Bridge-Traffic-Rush-Hour.mp4",
      date: "2023-06-08",
      duration: "20:10",
      vehicles: 312,
      highSpeed: 24,
      lowSpeed: 9,
      status: "processing",
    },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Uploads</CardTitle>
        <CardDescription>Your most recently analyzed traffic videos</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Video Name</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Vehicles</TableHead>
              <TableHead>High Speed</TableHead>
              <TableHead>Low Speed</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {videos.map((video) => (
              <TableRow key={video.id}>
                <TableCell className="font-medium">{video.name}</TableCell>
                <TableCell>{video.date}</TableCell>
                <TableCell>{video.duration}</TableCell>
                <TableCell>{video.vehicles}</TableCell>
                <TableCell>
                  <span className="text-red-500 font-medium">{video.highSpeed}</span>
                </TableCell>
                <TableCell>
                  <span className="text-blue-500 font-medium">{video.lowSpeed}</span>
                </TableCell>
                <TableCell>
                  <Badge variant={video.status === "completed" ? "outline" : "secondary"}>{video.status}</Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="sm" asChild>
                    <Link href={`/analysis?id=${video.id}`}>
                      <Eye className="h-4 w-4 mr-2" />
                      View
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
