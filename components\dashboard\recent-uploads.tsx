"use client"

import { useState, useEffect } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Eye, Play, Trash2 } from "lucide-react"
import Link from "next/link"
import { videoStore, type UploadedVideo } from "@/lib/video-store"
import { useToast } from "@/hooks/use-toast"

export function RecentUploads() {
  const [videos, setVideos] = useState<UploadedVideo[]>([])
  const [analyzing, setAnalyzing] = useState<string | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    const updateVideos = () => {
      setVideos(videoStore.getRecentVideos(10))
    }

    updateVideos()
    const unsubscribe = videoStore.subscribe(updateVideos)
    return unsubscribe
  }, [])

  const handleAnalyze = async (videoId: string) => {
    setAnalyzing(videoId)
    try {
      await videoStore.analyzeVideo(videoId)
      toast({
        title: "Analysis complete",
        description: "Video analysis has been completed successfully.",
      })
    } catch (error) {
      toast({
        title: "Analysis failed",
        description: "Failed to analyze video. Please try again.",
        variant: "destructive",
      })
    } finally {
      setAnalyzing(null)
    }
  }

  const handleDelete = (videoId: string) => {
    videoStore.removeVideo(videoId)
    toast({
      title: "Video deleted",
      description: "Video has been removed from your library.",
    })
  }

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = Math.floor(seconds % 60)
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString()
  }

  if (videos.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Uploads</CardTitle>
          <CardDescription>Your most recently uploaded traffic videos</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <p className="text-muted-foreground">No videos uploaded yet.</p>
            <p className="text-sm text-muted-foreground mt-1">
              Upload your first video to get started with traffic analysis.
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Uploads</CardTitle>
        <CardDescription>Your most recently uploaded traffic videos</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Video Name</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Duration</TableHead>
              <TableHead>Vehicles</TableHead>
              <TableHead>Speed Violations</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {videos.map((video) => (
              <TableRow key={video.id}>
                <TableCell className="font-medium">{video.name}</TableCell>
                <TableCell>{formatDate(video.uploadedAt)}</TableCell>
                <TableCell>{formatDuration(video.duration)}</TableCell>
                <TableCell>
                  {video.analyzed ? video.analysisResults?.totalVehicles || 0 : '-'}
                </TableCell>
                <TableCell>
                  {video.analyzed ? (
                    <span className="text-red-500 font-medium">
                      {video.analysisResults?.speedViolations || 0}
                    </span>
                  ) : '-'}
                </TableCell>
                <TableCell>
                  <Badge variant={video.analyzed ? "outline" : "secondary"}>
                    {video.analyzed ? "analyzed" : "uploaded"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex items-center justify-end gap-1">
                    {!video.analyzed && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleAnalyze(video.id)}
                        disabled={analyzing === video.id}
                      >
                        <Play className="h-4 w-4 mr-1" />
                        {analyzing === video.id ? 'Analyzing...' : 'Analyze'}
                      </Button>
                    )}
                    <Button variant="ghost" size="sm" asChild>
                      <Link href={`/analysis?id=${video.id}`}>
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Link>
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDelete(video.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
