import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/batch/batch-header"
import { BatchResults } from "@/components/batch/batch-results"
import { BatchSummary } from "@/components/batch/batch-summary"
import { BatchFilters } from "@/components/batch/batch-filters"

export default function BatchPage() {
  return (
    <div className="container mx-auto p-6">
      <BatchHeader />
      <BatchFilters className="mt-6" />
      <BatchSummary className="mt-6" />
      <BatchResults className="mt-6" />
    </div>
  )
}
