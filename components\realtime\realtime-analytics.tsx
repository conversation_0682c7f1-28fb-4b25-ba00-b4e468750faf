"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  LineChart, 
  Line, 
  AreaChart, 
  Area, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from "recharts"
import { 
  TrendingUp, 
  TrendingDown, 
  Activity, 
  Clock,
  Zap,
  Car,
  Truck,
  Bus,
  Bike
} from "lucide-react"
import { realtimeAnalysis, type RealtimeStats } from "@/lib/realtime-analysis"

interface HistoricalData {
  timestamp: number
  vehicleCount: number
  averageSpeed: number
  violations: number
  congestionLevel: number
}

export function RealtimeAnalytics() {
  const [stats, setStats] = useState<RealtimeStats>(realtimeAnalysis.getCurrentStats())
  const [historicalData, setHistoricalData] = useState<HistoricalData[]>([])
  const [timeRange, setTimeRange] = useState<'5m' | '15m' | '30m' | '1h'>('15m')
  const [chartType, setChartType] = useState<'line' | 'area' | 'bar'>('line')

  useEffect(() => {
    const unsubscribe = realtimeAnalysis.subscribe((newStats) => {
      setStats(newStats)
      
      // Add to historical data
      const newDataPoint: HistoricalData = {
        timestamp: Date.now(),
        vehicleCount: newStats.currentVehicles,
        averageSpeed: newStats.averageSpeed,
        violations: newStats.speedViolations,
        congestionLevel: newStats.congestionLevel
      }
      
      setHistoricalData(prev => {
        const updated = [...prev, newDataPoint]
        // Keep only data within the selected time range
        const timeLimit = getTimeLimit(timeRange)
        return updated.filter(data => data.timestamp > timeLimit)
      })
    })

    return unsubscribe
  }, [timeRange])

  const getTimeLimit = (range: string) => {
    const now = Date.now()
    switch (range) {
      case '5m': return now - 5 * 60 * 1000
      case '15m': return now - 15 * 60 * 1000
      case '30m': return now - 30 * 60 * 1000
      case '1h': return now - 60 * 60 * 1000
      default: return now - 15 * 60 * 1000
    }
  }

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    })
  }

  const getVehicleTypeData = () => {
    return Object.entries(stats.vehicleTypes).map(([type, count]) => ({
      name: type.charAt(0).toUpperCase() + type.slice(1),
      value: count,
      color: getVehicleTypeColor(type)
    }))
  }

  const getVehicleTypeColor = (type: string) => {
    switch (type) {
      case 'car': return '#3b82f6'
      case 'truck': return '#f59e0b'
      case 'bus': return '#10b981'
      case 'motorcycle': return '#ef4444'
      default: return '#6b7280'
    }
  }

  const getVehicleIcon = (type: string) => {
    switch (type) {
      case 'car': return <Car className="h-4 w-4" />
      case 'truck': return <Truck className="h-4 w-4" />
      case 'bus': return <Bus className="h-4 w-4" />
      case 'motorcycle': return <Bike className="h-4 w-4" />
      default: return <Car className="h-4 w-4" />
    }
  }

  const getTrendIndicator = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-500" />
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-500" />
    return <Activity className="h-4 w-4 text-gray-500" />
  }

  const previousData = historicalData[historicalData.length - 2] || historicalData[0]

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Real-time Analytics
              </CardTitle>
              <CardDescription>
                Live traffic analytics and trend analysis
              </CardDescription>
            </div>
            <div className="flex items-center gap-4">
              <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5m">Last 5m</SelectItem>
                  <SelectItem value="15m">Last 15m</SelectItem>
                  <SelectItem value="30m">Last 30m</SelectItem>
                  <SelectItem value="1h">Last 1h</SelectItem>
                </SelectContent>
              </Select>
              <Select value={chartType} onValueChange={(value: any) => setChartType(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="line">Line Chart</SelectItem>
                  <SelectItem value="area">Area Chart</SelectItem>
                  <SelectItem value="bar">Bar Chart</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Vehicle Count</CardTitle>
            {getTrendIndicator(stats.currentVehicles, previousData?.vehicleCount || 0)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.currentVehicles}</div>
            <p className="text-xs text-muted-foreground">
              {previousData ? 
                `${stats.currentVehicles > (previousData.vehicleCount || 0) ? '+' : ''}${stats.currentVehicles - (previousData.vehicleCount || 0)} from last update` : 
                'No previous data'
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Speed</CardTitle>
            {getTrendIndicator(stats.averageSpeed, previousData?.averageSpeed || 0)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageSpeed} km/h</div>
            <p className="text-xs text-muted-foreground">
              {previousData ? 
                `${stats.averageSpeed > (previousData.averageSpeed || 0) ? '+' : ''}${(stats.averageSpeed - (previousData.averageSpeed || 0)).toFixed(1)} km/h change` : 
                'No previous data'
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Speed Violations</CardTitle>
            {getTrendIndicator(stats.speedViolations, previousData?.violations || 0)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.speedViolations}</div>
            <p className="text-xs text-muted-foreground">
              {previousData ? 
                `${stats.speedViolations > (previousData.violations || 0) ? '+' : ''}${stats.speedViolations - (previousData.violations || 0)} violations` : 
                'No previous data'
              }
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Congestion Level</CardTitle>
            {getTrendIndicator(stats.congestionLevel, previousData?.congestionLevel || 0)}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.congestionLevel}%</div>
            <p className="text-xs text-muted-foreground">
              {stats.trafficFlow} traffic flow
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid gap-6 md:grid-cols-2">
        {/* Vehicle Count Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Vehicle Count Trend</CardTitle>
            <CardDescription>Real-time vehicle count over time</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              {chartType === 'line' ? (
                <LineChart data={historicalData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={formatTime}
                    interval="preserveStartEnd"
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={formatTime}
                    formatter={(value: any) => [value, 'Vehicles']}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="vehicleCount" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    dot={false}
                  />
                </LineChart>
              ) : chartType === 'area' ? (
                <AreaChart data={historicalData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={formatTime}
                    interval="preserveStartEnd"
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={formatTime}
                    formatter={(value: any) => [value, 'Vehicles']}
                  />
                  <Area 
                    type="monotone" 
                    dataKey="vehicleCount" 
                    stroke="#3b82f6" 
                    fill="#3b82f6" 
                    fillOpacity={0.3}
                  />
                </AreaChart>
              ) : (
                <BarChart data={historicalData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="timestamp" 
                    tickFormatter={formatTime}
                    interval="preserveStartEnd"
                  />
                  <YAxis />
                  <Tooltip 
                    labelFormatter={formatTime}
                    formatter={(value: any) => [value, 'Vehicles']}
                  />
                  <Bar dataKey="vehicleCount" fill="#3b82f6" />
                </BarChart>
              )}
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Speed Trend */}
        <Card>
          <CardHeader>
            <CardTitle>Average Speed Trend</CardTitle>
            <CardDescription>Real-time average speed over time</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={historicalData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="timestamp" 
                  tickFormatter={formatTime}
                  interval="preserveStartEnd"
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={formatTime}
                  formatter={(value: any) => [value, 'km/h']}
                />
                <Line 
                  type="monotone" 
                  dataKey="averageSpeed" 
                  stroke="#10b981" 
                  strokeWidth={2}
                  dot={false}
                />
                <Line 
                  type="monotone" 
                  dataKey="violations" 
                  stroke="#ef4444" 
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Vehicle Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Vehicle Type Distribution</CardTitle>
            <CardDescription>Current vehicle type breakdown</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={getVehicleTypeData()}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {getVehicleTypeData().map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Lane Occupancy */}
        <Card>
          <CardHeader>
            <CardTitle>Lane Occupancy</CardTitle>
            <CardDescription>Current lane distribution</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={Object.entries(stats.laneOccupancy).map(([lane, count]) => ({
                lane: `Lane ${parseInt(lane) + 1}`,
                vehicles: count
              }))}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="lane" />
                <YAxis />
                <Tooltip formatter={(value: any) => [value, 'Vehicles']} />
                <Bar dataKey="vehicles" fill="#8b5cf6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 