"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Play, Pause, SkipBack, SkipForward, Upload, Eye, EyeOff, Activity } from "lucide-react"
import { videoStore, type UploadedVideo } from "@/lib/video-store"
import { useSearchParams } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

interface VideoPlayerProps {
  videoId?: string
}

// Live vehicle detection data
interface LiveVehicle {
  id: string
  x: number
  y: number
  width: number
  height: number
  speed: number
  type: 'car' | 'truck' | 'bus' | 'motorcycle'
  color: string
  confidence: number
  direction: 'left' | 'right' | 'up' | 'down'
  lane: number
}

export function VideoPlayer({ videoId }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [video, setVideo] = useState<UploadedVideo | null>(null)
  const [liveVehicles, setLiveVehicles] = useState<LiveVehicle[]>([])
  const [showOverlay, setShowOverlay] = useState(true)
  const [liveAnalysis, setLiveAnalysis] = useState(true)
  const [totalVehicleCount, setTotalVehicleCount] = useState(0)
  const [speedViolations, setSpeedViolations] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>(0)
  const vehicleIdCounter = useRef(0)
  const searchParams = useSearchParams()
  const { toast } = useToast()

  // Get video ID from props or URL params
  const currentVideoId = videoId || (searchParams ? searchParams.get('id') : null)

  // Generate realistic live vehicle data
  const generateLiveVehicles = (time: number): LiveVehicle[] => {
    const vehicles: LiveVehicle[] = []
    const numVehicles = Math.floor(Math.random() * 6) + 4 // 4-9 vehicles

    for (let i = 0; i < numVehicles; i++) {
      vehicleIdCounter.current += 1
      const vehicleType = ['car', 'truck', 'bus', 'motorcycle'][Math.floor(Math.random() * 4)] as LiveVehicle['type']

      // Create realistic vehicle movement patterns
      const lane = i % 4
      const direction = Math.random() > 0.5 ? 'right' : 'left'
      const baseX = direction === 'right' ? -100 : 900
      const speed = 30 + Math.random() * 50 // 30-80 km/h

      // Calculate position based on time and speed
      const pixelsPerSecond = speed * 2 // Convert km/h to pixels/second (rough)
      const x = direction === 'right'
        ? baseX + (time * pixelsPerSecond) % 1000
        : baseX - (time * pixelsPerSecond) % 1000

      const y = 100 + lane * 70 + Math.sin(time + i) * 5 // Lane with slight movement

      // Determine color based on speed
      let color = '#22c55e' // Green for normal
      if (speed > 70) color = '#ef4444' // Red for high speed
      else if (speed > 55) color = '#f59e0b' // Orange for moderate high
      else if (speed < 35) color = '#3b82f6' // Blue for slow

      // Only show vehicles that are on screen
      if (x > -150 && x < 950 && y > 50 && y < 450) {
        vehicles.push({
          id: `vehicle-${vehicleIdCounter.current}`,
          x: Math.round(x),
          y: Math.round(y),
          width: vehicleType === 'truck' || vehicleType === 'bus' ? 100 : 70,
          height: vehicleType === 'truck' || vehicleType === 'bus' ? 50 : 35,
          speed: Math.round(speed),
          type: vehicleType,
          color,
          confidence: 0.8 + Math.random() * 0.2,
          direction: direction as 'left' | 'right',
          lane: lane
        })
      }
    }

    return vehicles
  }

  useEffect(() => {
    if (currentVideoId) {
      const foundVideo = videoStore.getVideo(currentVideoId)
      setVideo(foundVideo || null)
    }
  }, [currentVideoId])

  // Live analysis animation loop
  useEffect(() => {
    if (!liveAnalysis || !video) return

    const animate = () => {
      const time = currentTime
      const newVehicles = generateLiveVehicles(time)
      setLiveVehicles(newVehicles)

      // Update statistics
      setTotalVehicleCount(prev => Math.max(prev, newVehicles.length))
      setSpeedViolations(newVehicles.filter(v => v.speed > 70).length)

      if (isPlaying) {
        animationRef.current = requestAnimationFrame(animate)
      }
    }

    if (isPlaying) {
      animate()
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isPlaying, currentTime, liveAnalysis, video])

  useEffect(() => {
    const videoElement = videoRef.current
    if (!videoElement || !video) return

    const handleLoadedMetadata = () => {
      setDuration(videoElement.duration)
    }

    const handleTimeUpdate = () => {
      setCurrentTime(videoElement.currentTime)
    }

    const handleEnded = () => {
      setIsPlaying(false)
    }

    const handlePlay = () => {
      setIsPlaying(true)
    }

    const handlePause = () => {
      setIsPlaying(false)
    }

    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoElement.addEventListener('timeupdate', handleTimeUpdate)
    videoElement.addEventListener('ended', handleEnded)
    videoElement.addEventListener('play', handlePlay)
    videoElement.addEventListener('pause', handlePause)

    return () => {
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata)
      videoElement.removeEventListener('timeupdate', handleTimeUpdate)
      videoElement.removeEventListener('ended', handleEnded)
      videoElement.removeEventListener('play', handlePlay)
      videoElement.removeEventListener('pause', handlePause)
    }
  }, [video])

  // Live analysis drawing system
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || !showOverlay) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const drawLiveAnalysis = () => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw live vehicles with colored overlays
      liveVehicles.forEach((vehicle) => {
        // Use the vehicle's pre-calculated color
        const borderColor = vehicle.color
        const fillColor = vehicle.color + '20' // Add transparency

        // Draw vehicle bounding box
        ctx.strokeStyle = borderColor
        ctx.fillStyle = fillColor
        ctx.lineWidth = 3
        ctx.lineCap = "round"
        ctx.lineJoin = "round"

        // Filled rectangle for vehicle area
        ctx.fillRect(vehicle.x, vehicle.y, vehicle.width, vehicle.height)

        // Border rectangle
        ctx.strokeRect(vehicle.x, vehicle.y, vehicle.width, vehicle.height)

        // Draw speed indicator
        const speedBoxWidth = 85
        const speedBoxHeight = 25
        ctx.fillStyle = borderColor
        ctx.fillRect(vehicle.x, vehicle.y - speedBoxHeight - 5, speedBoxWidth, speedBoxHeight)

        // Speed text
        ctx.fillStyle = "white"
        ctx.font = "bold 14px Arial"
        ctx.textAlign = "left"
        ctx.fillText(`${vehicle.speed} km/h`, vehicle.x + 5, vehicle.y - 10)

        // Vehicle type label
        const typeBoxWidth = 70
        const typeBoxHeight = 18
        ctx.fillStyle = borderColor
        ctx.fillRect(vehicle.x, vehicle.y + vehicle.height + 2, typeBoxWidth, typeBoxHeight)

        ctx.fillStyle = "white"
        ctx.font = "11px Arial"
        ctx.fillText(`${vehicle.type.toUpperCase()}`, vehicle.x + 5, vehicle.y + vehicle.height + 15)

        // Speed violation indicator
        if (vehicle.speed > 70) {
          ctx.fillStyle = "#dc2626"
          ctx.font = "bold 12px Arial"
          ctx.fillText("⚠ VIOLATION", vehicle.x + vehicle.width + 5, vehicle.y + 15)
        }

        // Confidence indicator bar
        const confidenceWidth = vehicle.width * vehicle.confidence
        ctx.fillStyle = "rgba(34, 197, 94, 0.8)"
        ctx.fillRect(vehicle.x, vehicle.y - 3, confidenceWidth, 3)

        // Direction arrow
        ctx.fillStyle = borderColor
        ctx.font = "16px Arial"
        const arrow = vehicle.direction === 'right' ? '→' : '←'
        ctx.fillText(arrow, vehicle.x + vehicle.width/2 - 8, vehicle.y + vehicle.height/2 + 5)
      })

      // Draw live analysis statistics overlay
      ctx.fillStyle = "rgba(0, 0, 0, 0.8)"
      ctx.fillRect(10, 10, 220, 120)

      ctx.fillStyle = "white"
      ctx.font = "bold 16px Arial"
      ctx.fillText("🔴 LIVE ANALYSIS", 20, 35)

      ctx.font = "14px Arial"
      ctx.fillText(`Vehicles Detected: ${liveVehicles.length}`, 20, 55)

      const avgSpeed = liveVehicles.length > 0
        ? Math.round(liveVehicles.reduce((sum, v) => sum + v.speed, 0) / liveVehicles.length)
        : 0
      ctx.fillText(`Average Speed: ${avgSpeed} km/h`, 20, 75)

      const violations = liveVehicles.filter(v => v.speed > 70).length
      ctx.fillStyle = violations > 0 ? "#ef4444" : "white"
      ctx.fillText(`Speed Violations: ${violations}`, 20, 95)

      ctx.fillStyle = "white"
      ctx.font = "12px Arial"
      ctx.fillText(`Time: ${formatTime(currentTime)}`, 20, 115)
    }

    drawLiveAnalysis()
  }, [liveVehicles, showOverlay, currentTime])

  const togglePlayPause = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    if (isPlaying) {
      videoElement.pause()
    } else {
      videoElement.play()
    }
  }

  const handleSkipBack = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.currentTime = Math.max(0, videoElement.currentTime - 10)
  }

  const handleSkipForward = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.currentTime = Math.min(videoElement.duration, videoElement.currentTime + 10)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const videoElement = videoRef.current
    if (!videoElement) return

    const newTime = Number(e.target.value)
    videoElement.currentTime = newTime
    setCurrentTime(newTime)
  }

  if (!video) {
    return (
      <Card>
        <CardContent className="p-0 relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            <div className="absolute inset-0 bg-muted flex items-center justify-center">
              <div className="text-center p-4">
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-lg font-medium mb-2">No Video Selected</p>
                <p className="text-sm text-muted-foreground">
                  Upload a video or select one from your library to start analysis.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-0 relative">
        <div className="relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            <video
              ref={videoRef}
              src={video.url}
              className="w-full h-full object-cover"
              preload="metadata"
            />
            {/* Canvas for overlays */}
            <canvas
              ref={canvasRef}
              className="absolute top-0 left-0 w-full h-full pointer-events-none"
              width={854}
              height={480}
            />
          </div>
        </div>
        <div className="p-4 flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" onClick={handleSkipBack}>
                <SkipBack className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={togglePlayPause}>
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              <Button variant="ghost" size="icon" onClick={handleSkipForward}>
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max={duration || 100}
            value={currentTime}
            onChange={handleSeek}
            className="w-full"
          />
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Live Analysis: {liveVehicles.length} vehicles</span>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowOverlay(!showOverlay)}
              >
                {showOverlay ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showOverlay ? 'Hide' : 'Show'} Overlay
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLiveAnalysis(!liveAnalysis)}
              >
                <Activity className="h-4 w-4 mr-1" />
                {liveAnalysis ? 'Stop' : 'Start'} Analysis
              </Button>
            </div>
          </div>
          {liveAnalysis && (
            <div className="grid grid-cols-3 gap-2 text-xs">
              <Badge variant="outline">
                Vehicles: {liveVehicles.length}
              </Badge>
              <Badge variant={speedViolations > 0 ? "destructive" : "secondary"}>
                Violations: {speedViolations}
              </Badge>
              <Badge variant="outline">
                Total: {totalVehicleCount}
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
