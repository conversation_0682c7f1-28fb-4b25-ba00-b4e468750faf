"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Play, Pause, SkipBack, SkipForward, Upload } from "lucide-react"
import { videoStore, type UploadedVideo, type Vehicle } from "@/lib/video-store"
import { useSearchParams } from "next/navigation"

interface VideoPlayerProps {
  videoId?: string
}

export function VideoPlayer({ videoId }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [video, setVideo] = useState<UploadedVideo | null>(null)
  const [vehicles, setVehicles] = useState<Vehicle[]>([])
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const searchParams = useSearchParams()

  // Get video ID from props or URL params
  const currentVideoId = videoId || (searchParams ? searchParams.get('id') : null)

  useEffect(() => {
    if (currentVideoId) {
      const foundVideo = videoStore.getVideo(currentVideoId)
      setVideo(foundVideo || null)

      if (foundVideo?.analysisResults) {
        setVehicles(foundVideo.analysisResults.vehicles)
      }
    }
  }, [currentVideoId])

  useEffect(() => {
    const videoElement = videoRef.current
    if (!videoElement || !video) return

    const handleLoadedMetadata = () => {
      setDuration(videoElement.duration)
    }

    const handleTimeUpdate = () => {
      setCurrentTime(videoElement.currentTime)
    }

    const handleEnded = () => {
      setIsPlaying(false)
    }

    const handlePlay = () => {
      setIsPlaying(true)
    }

    const handlePause = () => {
      setIsPlaying(false)
    }

    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoElement.addEventListener('timeupdate', handleTimeUpdate)
    videoElement.addEventListener('ended', handleEnded)
    videoElement.addEventListener('play', handlePlay)
    videoElement.addEventListener('pause', handlePause)

    return () => {
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata)
      videoElement.removeEventListener('timeupdate', handleTimeUpdate)
      videoElement.removeEventListener('ended', handleEnded)
      videoElement.removeEventListener('play', handlePlay)
      videoElement.removeEventListener('pause', handlePause)
    }
  }, [video])

  // Draw overlay with vehicle detection boxes
  useEffect(() => {
    const canvas = canvasRef.current
    const videoElement = videoRef.current
    if (!canvas || !videoElement || !video?.analyzed) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const drawOverlay = () => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Filter vehicles that should be visible at current time
      const currentVehicles = vehicles.filter(vehicle =>
        Math.abs(vehicle.timestamp - currentTime) < 1 // Show vehicles within 1 second
      )

      // Draw bounding boxes for vehicles
      currentVehicles.forEach((vehicle) => {
        // Set color based on speed (assuming speed limit of 60 km/h)
        let color = "#22c55e" // green for normal
        if (vehicle.speed > 60) color = "#ef4444" // red for high speed
        if (vehicle.speed < 30) color = "#3b82f6" // blue for low speed

        // Draw bounding box
        ctx.strokeStyle = color
        ctx.lineWidth = 2
        ctx.strokeRect(vehicle.x, vehicle.y, vehicle.width, vehicle.height)

        // Draw speed label
        ctx.fillStyle = color
        ctx.fillRect(vehicle.x, vehicle.y - 20, 60, 20)
        ctx.fillStyle = "white"
        ctx.font = "12px Arial"
        ctx.fillText(`${vehicle.speed} km/h`, vehicle.x + 5, vehicle.y - 5)

        // Draw vehicle type
        ctx.fillStyle = color
        ctx.fillRect(vehicle.x, vehicle.y + vehicle.height, 60, 15)
        ctx.fillStyle = "white"
        ctx.font = "10px Arial"
        ctx.fillText(vehicle.type, vehicle.x + 5, vehicle.y + vehicle.height + 10)
      })
    }

    drawOverlay()
  }, [currentTime, vehicles, video])

  const togglePlayPause = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    if (isPlaying) {
      videoElement.pause()
    } else {
      videoElement.play()
    }
  }

  const handleSkipBack = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.currentTime = Math.max(0, videoElement.currentTime - 10)
  }

  const handleSkipForward = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.currentTime = Math.min(videoElement.duration, videoElement.currentTime + 10)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const videoElement = videoRef.current
    if (!videoElement) return

    const newTime = Number(e.target.value)
    videoElement.currentTime = newTime
    setCurrentTime(newTime)
  }

  if (!video) {
    return (
      <Card>
        <CardContent className="p-0 relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            <div className="absolute inset-0 bg-muted flex items-center justify-center">
              <div className="text-center p-4">
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-lg font-medium mb-2">No Video Selected</p>
                <p className="text-sm text-muted-foreground">
                  Upload a video or select one from your library to start analysis.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-0 relative">
        <div className="relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            <video
              ref={videoRef}
              src={video.url}
              className="w-full h-full object-cover"
              preload="metadata"
            />
            {/* Canvas for overlays */}
            <canvas
              ref={canvasRef}
              className="absolute top-0 left-0 w-full h-full pointer-events-none"
              width={854}
              height={480}
            />
          </div>
        </div>
        <div className="p-4 flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" onClick={handleSkipBack}>
                <SkipBack className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={togglePlayPause}>
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              <Button variant="ghost" size="icon" onClick={handleSkipForward}>
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max={duration || 100}
            value={currentTime}
            onChange={handleSeek}
            className="w-full"
          />
          {video.analyzed && (
            <div className="text-xs text-muted-foreground">
              Analysis: {vehicles.length} vehicles detected
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
