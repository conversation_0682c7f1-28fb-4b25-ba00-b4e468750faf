"use client"

import type React from "react"
import { useState, useRef, useEffect, useCallback } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Play, Pause, SkipBack, SkipForward, Upload, Download, Eye, EyeOff } from "lucide-react"
import { videoStore, type UploadedVideo, type Vehicle } from "@/lib/video-store"
import { useSearchParams } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

interface VideoPlayerProps {
  videoId?: string
}

// Enhanced vehicle tracking with live speed analysis
interface LiveVehicle extends Vehicle {
  trail: { x: number; y: number; timestamp: number }[]
  currentSpeed: number
  speedHistory: number[]
  isVisible: boolean
  lastSeen: number
}

export function VideoPlayer({ videoId }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [video, setVideo] = useState<UploadedVideo | null>(null)
  const [vehicles, setVehicles] = useState<Vehicle[]>([])
  const [liveVehicles, setLiveVehicles] = useState<LiveVehicle[]>([])
  const [showOverlay, setShowOverlay] = useState(true)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisProgress, setAnalysisProgress] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationFrameRef = useRef<number>(0)
  const searchParams = useSearchParams()
  const { toast } = useToast()

  // Get video ID from props or URL params
  const currentVideoId = videoId || (searchParams ? searchParams.get('id') : null)

  // Generate live vehicle tracking data
  const generateLiveVehicles = useCallback((time: number): LiveVehicle[] => {
    if (!video) return []

    const numVehicles = Math.floor(Math.random() * 8) + 3 // 3-10 vehicles
    const liveVehicles: LiveVehicle[] = []

    for (let i = 0; i < numVehicles; i++) {
      const vehicleId = `live-${i}-${Math.floor(time)}`
      const vehicleType = ['car', 'truck', 'bus', 'motorcycle'][Math.floor(Math.random() * 4)] as Vehicle['type']

      // Simulate vehicle movement across the screen
      const progress = (time * 0.1 + i * 0.2) % 1
      const lane = i % 3
      const x = progress * 800 + Math.sin(time * 0.5 + i) * 20
      const y = 150 + lane * 80 + Math.sin(time * 0.3 + i) * 10

      // Calculate realistic speed based on movement
      const baseSpeed = 45 + Math.sin(time * 0.2 + i) * 25 // 20-70 km/h
      const currentSpeed = Math.max(15, baseSpeed + Math.random() * 10 - 5)

      const vehicle: LiveVehicle = {
        id: vehicleId,
        type: vehicleType,
        speed: Math.round(currentSpeed),
        timestamp: time,
        x: Math.round(x),
        y: Math.round(y),
        width: vehicleType === 'truck' || vehicleType === 'bus' ? 120 : 80,
        height: vehicleType === 'truck' || vehicleType === 'bus' ? 60 : 40,
        confidence: 0.85 + Math.random() * 0.15,
        trail: [],
        currentSpeed: Math.round(currentSpeed),
        speedHistory: [Math.round(currentSpeed)],
        isVisible: progress > 0.05 && progress < 0.95,
        lastSeen: time
      }

      liveVehicles.push(vehicle)
    }

    return liveVehicles
  }, [video])

  useEffect(() => {
    if (currentVideoId) {
      const foundVideo = videoStore.getVideo(currentVideoId)
      setVideo(foundVideo || null)

      if (foundVideo?.analysisResults) {
        setVehicles(foundVideo.analysisResults.vehicles)
      }
    }
  }, [currentVideoId])

  useEffect(() => {
    const videoElement = videoRef.current
    if (!videoElement || !video) return

    const handleLoadedMetadata = () => {
      setDuration(videoElement.duration)
    }

    const handleTimeUpdate = () => {
      const time = videoElement.currentTime
      setCurrentTime(time)

      // Generate live vehicle data for current time
      if (showOverlay) {
        const newLiveVehicles = generateLiveVehicles(time)
        setLiveVehicles(newLiveVehicles)
      }
    }

    const handleEnded = () => {
      setIsPlaying(false)
    }

    const handlePlay = () => {
      setIsPlaying(true)
    }

    const handlePause = () => {
      setIsPlaying(false)
    }

    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoElement.addEventListener('timeupdate', handleTimeUpdate)
    videoElement.addEventListener('ended', handleEnded)
    videoElement.addEventListener('play', handlePlay)
    videoElement.addEventListener('pause', handlePause)

    return () => {
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata)
      videoElement.removeEventListener('timeupdate', handleTimeUpdate)
      videoElement.removeEventListener('ended', handleEnded)
      videoElement.removeEventListener('play', handlePlay)
      videoElement.removeEventListener('pause', handlePause)
    }
  }, [video, showOverlay, generateLiveVehicles])

  // Advanced drawing system with live vehicle tracking
  useEffect(() => {
    const canvas = canvasRef.current
    const videoElement = videoRef.current
    if (!canvas || !videoElement || !showOverlay) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const drawLiveAnalysis = () => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw live vehicles with enhanced visualization
      liveVehicles.forEach((vehicle, index) => {
        if (!vehicle.isVisible) return

        // Color coding based on speed and vehicle type
        let borderColor = "#22c55e" // green for normal speed
        let fillColor = "rgba(34, 197, 94, 0.1)"

        if (vehicle.currentSpeed > 70) {
          borderColor = "#ef4444" // red for high speed
          fillColor = "rgba(239, 68, 68, 0.2)"
        } else if (vehicle.currentSpeed < 25) {
          borderColor = "#3b82f6" // blue for low speed
          fillColor = "rgba(59, 130, 246, 0.2)"
        } else if (vehicle.currentSpeed > 55) {
          borderColor = "#f59e0b" // orange for moderate high speed
          fillColor = "rgba(245, 158, 11, 0.15)"
        }

        // Vehicle type specific styling
        let lineWidth = 2
        if (vehicle.type === 'truck' || vehicle.type === 'bus') {
          lineWidth = 3
          borderColor = vehicle.currentSpeed > 60 ? "#dc2626" : "#16a34a"
        }

        // Draw vehicle bounding box with rounded corners
        ctx.strokeStyle = borderColor
        ctx.fillStyle = fillColor
        ctx.lineWidth = lineWidth
        ctx.lineCap = "round"
        ctx.lineJoin = "round"

        // Filled rectangle for vehicle area
        ctx.fillRect(vehicle.x, vehicle.y, vehicle.width, vehicle.height)

        // Border rectangle
        ctx.strokeRect(vehicle.x, vehicle.y, vehicle.width, vehicle.height)

        // Draw speed indicator with live updates
        const speedBoxWidth = 80
        const speedBoxHeight = 25
        ctx.fillStyle = borderColor
        ctx.fillRect(vehicle.x, vehicle.y - speedBoxHeight - 5, speedBoxWidth, speedBoxHeight)

        // Speed text
        ctx.fillStyle = "white"
        ctx.font = "bold 14px Arial"
        ctx.textAlign = "left"
        ctx.fillText(`${vehicle.currentSpeed} km/h`, vehicle.x + 5, vehicle.y - 10)

        // Vehicle ID and type
        const typeBoxWidth = 70
        const typeBoxHeight = 18
        ctx.fillStyle = borderColor
        ctx.fillRect(vehicle.x, vehicle.y + vehicle.height + 2, typeBoxWidth, typeBoxHeight)

        ctx.fillStyle = "white"
        ctx.font = "11px Arial"
        ctx.fillText(`${vehicle.type.toUpperCase()}`, vehicle.x + 5, vehicle.y + vehicle.height + 15)

        // Draw vehicle trail/path
        if (vehicle.trail && vehicle.trail.length > 1) {
          ctx.strokeStyle = borderColor
          ctx.lineWidth = 1
          ctx.globalAlpha = 0.5
          ctx.beginPath()
          vehicle.trail.forEach((point, i) => {
            if (i === 0) {
              ctx.moveTo(point.x, point.y)
            } else {
              ctx.lineTo(point.x, point.y)
            }
          })
          ctx.stroke()
          ctx.globalAlpha = 1
        }

        // Speed violation indicator
        if (vehicle.currentSpeed > 70) {
          ctx.fillStyle = "#dc2626"
          ctx.font = "bold 12px Arial"
          ctx.fillText("⚠ VIOLATION", vehicle.x + vehicle.width + 5, vehicle.y + 15)
        }

        // Confidence indicator
        const confidenceWidth = vehicle.width * vehicle.confidence
        ctx.fillStyle = "rgba(34, 197, 94, 0.6)"
        ctx.fillRect(vehicle.x, vehicle.y - 3, confidenceWidth, 3)
      })

      // Draw analysis statistics overlay
      ctx.fillStyle = "rgba(0, 0, 0, 0.7)"
      ctx.fillRect(10, 10, 200, 100)

      ctx.fillStyle = "white"
      ctx.font = "14px Arial"
      ctx.fillText("Live Analysis", 20, 30)
      ctx.font = "12px Arial"
      ctx.fillText(`Vehicles: ${liveVehicles.filter(v => v.isVisible).length}`, 20, 50)

      const avgSpeed = liveVehicles.length > 0
        ? Math.round(liveVehicles.reduce((sum, v) => sum + v.currentSpeed, 0) / liveVehicles.length)
        : 0
      ctx.fillText(`Avg Speed: ${avgSpeed} km/h`, 20, 70)

      const violations = liveVehicles.filter(v => v.currentSpeed > 70).length
      ctx.fillText(`Violations: ${violations}`, 20, 90)

      // Request next frame for smooth animation
      if (isPlaying) {
        animationFrameRef.current = requestAnimationFrame(drawLiveAnalysis)
      }
    }

    drawLiveAnalysis()

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [liveVehicles, isPlaying, showOverlay])

  const togglePlayPause = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    if (isPlaying) {
      videoElement.pause()
    } else {
      videoElement.play()
    }
  }

  const handleSkipBack = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.currentTime = Math.max(0, videoElement.currentTime - 10)
  }

  const handleSkipForward = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.currentTime = Math.min(videoElement.duration, videoElement.currentTime + 10)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const videoElement = videoRef.current
    if (!videoElement) return

    const newTime = Number(e.target.value)
    videoElement.currentTime = newTime
    setCurrentTime(newTime)
  }

  if (!video) {
    return (
      <Card>
        <CardContent className="p-0 relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            <div className="absolute inset-0 bg-muted flex items-center justify-center">
              <div className="text-center p-4">
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-lg font-medium mb-2">No Video Selected</p>
                <p className="text-sm text-muted-foreground">
                  Upload a video or select one from your library to start analysis.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-0 relative">
        <div className="relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            <video
              ref={videoRef}
              src={video.url}
              className="w-full h-full object-cover"
              preload="metadata"
            />
            {/* Canvas for overlays */}
            <canvas
              ref={canvasRef}
              className="absolute top-0 left-0 w-full h-full pointer-events-none"
              width={854}
              height={480}
            />
          </div>
        </div>
        <div className="p-4 flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" onClick={handleSkipBack}>
                <SkipBack className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={togglePlayPause}>
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              <Button variant="ghost" size="icon" onClick={handleSkipForward}>
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max={duration || 100}
            value={currentTime}
            onChange={handleSeek}
            className="w-full"
          />
          {video.analyzed && (
            <div className="text-xs text-muted-foreground">
              Analysis: {vehicles.length} vehicles detected
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
