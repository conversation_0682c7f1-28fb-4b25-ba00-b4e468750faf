"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Play, Pause, SkipBack, SkipForward, Upload, Eye, EyeOff, Activity } from "lucide-react"
import { videoStore, type UploadedVideo } from "@/lib/video-store"
import { useSearchParams } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

interface VideoPlayerProps {
  videoId?: string
}

// Live vehicle detection data
interface LiveVehicle {
  id: string
  x: number
  y: number
  width: number
  height: number
  speed: number
  type: 'car' | 'truck' | 'bus' | 'motorcycle'
  color: string
  confidence: number
  direction: 'left' | 'right' | 'up' | 'down'
  lane: number
}

export function VideoPlayer({ videoId }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [video, setVideo] = useState<UploadedVideo | null>(null)
  const [liveVehicles, setLiveVehicles] = useState<LiveVehicle[]>([])
  const [showOverlay, setShowOverlay] = useState(true)
  const [liveAnalysis, setLiveAnalysis] = useState(true)
  const [totalVehicleCount, setTotalVehicleCount] = useState(0)
  const [speedViolations, setSpeedViolations] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>(0)
  const vehicleIdCounter = useRef(0)c
  const searchParams = useSearchParams()
  const { toast } = useToast()

  // Get video ID from props or URL params
  const currentVideoId = videoId || (searchParams ? searchParams.get('id') : null)

  // Generate vehicles with realistic traffic patterns and proper sizing
  const generateLiveVehicles = (time: number): LiveVehicle[] => {
    const vehicles: LiveVehicle[] = []
    const numVehicles = 8 // More vehicles for realistic traffic

    for (let i = 0; i < numVehicles; i++) {
      const vehicleType = ['car', 'truck', 'bus', 'motorcycle'][i % 4] as LiveVehicle['type']

      // Create realistic traffic lanes and movement patterns
      const lane = i % 4 // 4 lanes for more realistic traffic
      const isHorizontal = lane < 2 // Top 2 lanes horizontal, bottom 2 vertical

      // Dynamic speed with realistic variation
      const baseSpeed = 35 + (i * 5) + Math.sin(time * 0.1 + i) * 15 // 20-70 km/h
      const speed = Math.max(25, Math.min(75, baseSpeed))

      let x, y, direction: 'left' | 'right' | 'up' | 'down'

      if (isHorizontal) {
        // Horizontal movement (left-right)
        direction = i % 2 === 0 ? 'right' : 'left'
        const pixelsPerSecond = speed * 0.15 // Slower for better visibility

        if (direction === 'right') {
          x = (-100 + (time * pixelsPerSecond + i * 150)) % 900
        } else {
          x = (800 - (time * pixelsPerSecond + i * 150)) % 900
        }
        y = 80 + lane * 60 // Horizontal lanes
      } else {
        // Vertical movement (up-down) for intersection effect
        direction = i % 2 === 0 ? 'down' : 'up'
        const pixelsPerSecond = speed * 0.12

        x = 200 + (lane - 2) * 120 // Vertical lanes
        if (direction === 'down') {
          y = (-80 + (time * pixelsPerSecond + i * 100)) % 400
        } else {
          y = (350 - (time * pixelsPerSecond + i * 100)) % 400
        }
      }

      // Realistic vehicle dimensions based on type
      let width, height
      switch (vehicleType) {
        case 'truck':
          width = isHorizontal ? 120 : 80
          height = isHorizontal ? 60 : 140
          break
        case 'bus':
          width = isHorizontal ? 140 : 80
          height = isHorizontal ? 70 : 160
          break
        case 'motorcycle':
          width = isHorizontal ? 50 : 30
          height = isHorizontal ? 30 : 60
          break
        default: // car
          width = isHorizontal ? 80 : 50
          height = isHorizontal ? 50 : 90
      }

      // Dynamic color based on current speed
      let color = '#22c55e' // Green for normal (35-50)
      if (speed > 60) color = '#ef4444' // Red for high speed
      else if (speed > 45) color = '#f59e0b' // Orange for moderate high
      else if (speed < 35) color = '#3b82f6' // Blue for slow

      // Only show vehicles that are visible on screen
      if (x > -width && x < 800 + width && y > -height && y < 400 + height) {
        vehicles.push({
          id: `vehicle-${i}`,
          x: Math.round(x),
          y: Math.round(y),
          width,
          height,
          speed: Math.round(speed),
          type: vehicleType,
          color,
          confidence: 0.85 + Math.sin(time * 0.15 + i) * 0.1,
          direction: direction as 'left' | 'right',
          lane: lane
        })
      }
    }

    return vehicles
  }

  useEffect(() => {
    if (currentVideoId) {
      const foundVideo = videoStore.getVideo(currentVideoId)
      setVideo(foundVideo || null)
    }
  }, [currentVideoId])

  // Slower live analysis animation loop for better visibility
  useEffect(() => {
    if (!liveAnalysis || !video) return

    let lastUpdateTime = 0
    const updateInterval = 200 // Update every 200ms instead of every frame for slower, more visible changes

    const animate = (timestamp: number) => {
      if (timestamp - lastUpdateTime >= updateInterval) {
        const time = currentTime
        const newVehicles = generateLiveVehicles(time)
        setLiveVehicles(newVehicles)

        // Update statistics
        setTotalVehicleCount(prev => Math.max(prev, newVehicles.length))
        setSpeedViolations(newVehicles.filter(v => v.speed > 65).length) // Lower threshold for more visible violations

        lastUpdateTime = timestamp
      }

      if (isPlaying && liveAnalysis) {
        animationRef.current = requestAnimationFrame(animate)
      }
    }

    if (isPlaying) {
      animationRef.current = requestAnimationFrame(animate)
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isPlaying, currentTime, liveAnalysis, video])

  useEffect(() => {
    const videoElement = videoRef.current
    if (!videoElement || !video) return

    const handleLoadedMetadata = () => {
      setDuration(videoElement.duration)
    }

    const handleTimeUpdate = () => {
      setCurrentTime(videoElement.currentTime)
    }

    const handleEnded = () => {
      setIsPlaying(false)
    }

    const handlePlay = () => {
      setIsPlaying(true)
    }

    const handlePause = () => {
      setIsPlaying(false)
    }

    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoElement.addEventListener('timeupdate', handleTimeUpdate)
    videoElement.addEventListener('ended', handleEnded)
    videoElement.addEventListener('play', handlePlay)
    videoElement.addEventListener('pause', handlePause)

    return () => {
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata)
      videoElement.removeEventListener('timeupdate', handleTimeUpdate)
      videoElement.removeEventListener('ended', handleEnded)
      videoElement.removeEventListener('play', handlePlay)
      videoElement.removeEventListener('pause', handlePause)
    }
  }, [video])

  // Fixed live analysis drawing system with proper canvas sizing and alignment
  useEffect(() => {
    const canvas = canvasRef.current
    const video = videoRef.current
    if (!canvas || !showOverlay || !video) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Ensure canvas matches video dimensions
    const rect = video.getBoundingClientRect()
    canvas.width = rect.width
    canvas.height = rect.height
    canvas.style.width = rect.width + 'px'
    canvas.style.height = rect.height + 'px'

    const drawLiveAnalysis = () => {
      // Clear canvas with proper dimensions
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Debug: Draw a test rectangle to verify canvas is working
      ctx.fillStyle = "rgba(255, 0, 0, 0.3)"
      ctx.fillRect(10, 10, 100, 50)
      ctx.strokeStyle = "red"
      ctx.lineWidth = 2
      ctx.strokeRect(10, 10, 100, 50)
      ctx.fillStyle = "white"
      ctx.font = "14px Arial"
      ctx.fillText("Canvas Working", 15, 35)

      // Draw live vehicles with colored speed tracking bars
      liveVehicles.forEach((vehicle, index) => {
        // Scale vehicle position to canvas size
        const scaleX = canvas.width / 800 // Assuming 800px reference width
        const scaleY = canvas.height / 400 // Assuming 400px reference height

        const scaledX = vehicle.x * scaleX
        const scaledY = vehicle.y * scaleY
        const scaledWidth = vehicle.width * scaleX
        const scaledHeight = vehicle.height * scaleY

        // Debug logging for first vehicle
        if (index === 0) {
          console.log('Vehicle 0:', {
            original: { x: vehicle.x, y: vehicle.y, w: vehicle.width, h: vehicle.height },
            scaled: { x: scaledX, y: scaledY, w: scaledWidth, h: scaledHeight },
            canvas: { w: canvas.width, h: canvas.height },
            speed: vehicle.speed,
            color: vehicle.color
          })
        }

        const borderColor = vehicle.color

        // Draw vehicle body with proper proportions
        ctx.fillStyle = borderColor + '50' // Semi-transparent fill
        ctx.strokeStyle = borderColor
        ctx.lineWidth = Math.max(2, scaledWidth/20) // Proportional border
        ctx.fillRect(scaledX, scaledY, scaledWidth, scaledHeight)
        ctx.strokeRect(scaledX, scaledY, scaledWidth, scaledHeight)

        // MAIN FEATURE: Speed bar that perfectly matches vehicle size
        const barPadding = Math.max(4, scaledWidth/15) // Proportional padding
        const barWidth = scaledWidth - (barPadding * 2) // Bar width matches vehicle width
        const barHeight = Math.max(8, scaledHeight/4) // Bar height proportional to vehicle
        const barX = scaledX + barPadding // Aligned with vehicle edges
        const barY = scaledY + scaledHeight/2 - barHeight/2 // Centered vertically

        // Background bar (dark for contrast) - full vehicle width
        ctx.fillStyle = "rgba(0, 0, 0, 0.8)"
        ctx.fillRect(barX, barY, barWidth, barHeight)

        // Speed tracking bar - fills based on speed percentage
        const maxSpeed = 75 // Realistic max speed
        const speedPercentage = Math.min(vehicle.speed / maxSpeed, 1)
        const speedBarWidth = barWidth * speedPercentage // Bar fills proportionally

        // Colored speed bar that matches vehicle width
        ctx.fillStyle = borderColor
        ctx.fillRect(barX, barY, speedBarWidth, barHeight)

        // White border around entire speed bar area
        ctx.strokeStyle = "white"
        ctx.lineWidth = Math.max(1, scaledWidth/40)
        ctx.strokeRect(barX, barY, barWidth, barHeight)

        // Gradient effect for better visibility
        const gradient = ctx.createLinearGradient(barX, barY, barX + speedBarWidth, barY)
        gradient.addColorStop(0, borderColor)
        gradient.addColorStop(1, borderColor + 'CC') // Slightly transparent end
        ctx.fillStyle = gradient
        ctx.fillRect(barX, barY, speedBarWidth, barHeight)

        // Speed text properly sized for vehicle
        const centerX = scaledX + scaledWidth/2
        const centerY = scaledY + scaledHeight/2 - barHeight/2 - 5 // Above the speed bar

        // Speed number proportional to vehicle size
        const fontSize = Math.max(12, Math.min(scaledWidth/4, scaledHeight/3))
        ctx.font = `bold ${fontSize}px Arial`
        ctx.textAlign = "center"

        // Black outline for readability
        ctx.strokeStyle = "black"
        ctx.lineWidth = Math.max(2, fontSize/6)
        ctx.strokeText(`${vehicle.speed}`, centerX, centerY)

        // White fill for contrast
        ctx.fillStyle = "white"
        ctx.fillText(`${vehicle.speed}`, centerX, centerY)

        // "km/h" text below speed bar
        const smallFontSize = Math.max(8, fontSize * 0.6)
        ctx.font = `${smallFontSize}px Arial`
        const kmhY = scaledY + scaledHeight/2 + barHeight/2 + smallFontSize + 2

        ctx.strokeStyle = "black"
        ctx.lineWidth = Math.max(1, smallFontSize/8)
        ctx.strokeText("km/h", centerX, kmhY)

        ctx.fillStyle = "white"
        ctx.fillText("km/h", centerX, kmhY)

        // Vehicle type above vehicle
        ctx.fillStyle = "white"
        ctx.font = `bold ${Math.max(12, scaledWidth/6)}px Arial`
        ctx.strokeStyle = borderColor
        ctx.lineWidth = 3
        ctx.strokeText(vehicle.type.toUpperCase(), centerX, scaledY - 10)
        ctx.fillText(vehicle.type.toUpperCase(), centerX, scaledY - 10)

        // Speed violation alert
        if (vehicle.speed > 65) {
          ctx.strokeStyle = "#ff0000"
          ctx.lineWidth = 6
          ctx.setLineDash([10, 10])
          ctx.strokeRect(scaledX - 5, scaledY - 5, scaledWidth + 10, scaledHeight + 10)
          ctx.setLineDash([])

          // Violation text
          ctx.fillStyle = "#ff0000"
          ctx.font = `bold ${Math.max(14, scaledWidth/5)}px Arial`
          ctx.strokeStyle = "white"
          ctx.lineWidth = 3
          ctx.strokeText("⚠ VIOLATION", centerX, scaledY + scaledHeight + 25)
          ctx.fillText("⚠ VIOLATION", centerX, scaledY + scaledHeight + 25)
        }

        // Direction arrow proportional to vehicle
        const arrowSize = Math.max(12, scaledWidth/5)
        ctx.font = `bold ${arrowSize}px Arial`
        ctx.strokeStyle = "black"
        ctx.lineWidth = Math.max(1, arrowSize/10)
        const arrow = vehicle.direction === 'right' ? '→' : vehicle.direction === 'left' ? '←' :
                     vehicle.direction === 'down' ? '↓' : '↑'
        const arrowY = scaledY + scaledHeight + arrowSize + 5
        ctx.strokeText(arrow, centerX, arrowY)
        ctx.fillStyle = borderColor
        ctx.fillText(arrow, centerX, arrowY)

        // Vehicle ID badge
        const idWidth = 30
        const idHeight = 20
        ctx.fillStyle = borderColor
        ctx.fillRect(scaledX, scaledY, idWidth, idHeight)
        ctx.strokeStyle = "white"
        ctx.lineWidth = 2
        ctx.strokeRect(scaledX, scaledY, idWidth, idHeight)
        ctx.fillStyle = "white"
        ctx.font = "bold 12px Arial"
        ctx.textAlign = "center"
        ctx.fillText(`${vehicle.id.split('-')[1]}`, scaledX + idWidth/2, scaledY + 14)
      })

      // Fixed statistics overlay with proper positioning
      const statsWidth = 300
      const statsHeight = 120
      const statsX = 20
      const statsY = 20

      ctx.fillStyle = "rgba(0, 0, 0, 0.85)"
      ctx.fillRect(statsX, statsY, statsWidth, statsHeight)

      ctx.strokeStyle = "#22c55e"
      ctx.lineWidth = 3
      ctx.strokeRect(statsX, statsY, statsWidth, statsHeight)

      ctx.fillStyle = "white"
      ctx.font = "bold 20px Arial"
      ctx.textAlign = "left"
      ctx.fillText("🔴 LIVE ANALYSIS", statsX + 15, statsY + 30)

      ctx.font = "16px Arial"
      ctx.fillText(`Vehicles: ${liveVehicles.length}`, statsX + 15, statsY + 55)

      const avgSpeed = liveVehicles.length > 0
        ? Math.round(liveVehicles.reduce((sum, v) => sum + v.speed, 0) / liveVehicles.length)
        : 0
      ctx.fillText(`Avg Speed: ${avgSpeed} km/h`, statsX + 15, statsY + 80)

      const violations = liveVehicles.filter(v => v.speed > 65).length
      ctx.fillStyle = violations > 0 ? "#ef4444" : "#22c55e"
      ctx.fillText(`Violations: ${violations}`, statsX + 15, statsY + 105)

      // Fixed color legend
      const legendWidth = 200
      const legendHeight = 110
      const legendX = canvas.width - legendWidth - 20
      const legendY = 20

      ctx.fillStyle = "rgba(0, 0, 0, 0.85)"
      ctx.fillRect(legendX, legendY, legendWidth, legendHeight)
      ctx.strokeStyle = "white"
      ctx.lineWidth = 2
      ctx.strokeRect(legendX, legendY, legendWidth, legendHeight)

      ctx.fillStyle = "white"
      ctx.font = "bold 16px Arial"
      ctx.textAlign = "left"
      ctx.fillText("Speed Colors:", legendX + 15, legendY + 25)

      const legendItems = [
        { color: '#3b82f6', text: 'Slow (20-35 km/h)' },
        { color: '#22c55e', text: 'Normal (35-50)' },
        { color: '#f59e0b', text: 'Fast (50-65)' },
        { color: '#ef4444', text: 'Violation (65+)' }
      ]

      legendItems.forEach((item, i) => {
        const itemY = legendY + 45 + i * 18
        ctx.fillStyle = item.color
        ctx.fillRect(legendX + 15, itemY - 8, 20, 12)
        ctx.strokeStyle = "white"
        ctx.lineWidth = 1
        ctx.strokeRect(legendX + 15, itemY - 8, 20, 12)
        ctx.fillStyle = "white"
        ctx.font = "14px Arial"
        ctx.fillText(item.text, legendX + 45, itemY)
      })
    }

    drawLiveAnalysis()
  }, [liveVehicles, showOverlay, currentTime, video])

  const togglePlayPause = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    if (isPlaying) {
      videoElement.pause()
    } else {
      videoElement.play()
    }
  }

  const handleSkipBack = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.currentTime = Math.max(0, videoElement.currentTime - 10)
  }

  const handleSkipForward = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.currentTime = Math.min(videoElement.duration, videoElement.currentTime + 10)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const videoElement = videoRef.current
    if (!videoElement) return

    const newTime = Number(e.target.value)
    videoElement.currentTime = newTime
    setCurrentTime(newTime)
  }

  if (!video) {
    return (
      <Card>
        <CardContent className="p-0 relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            <div className="absolute inset-0 bg-muted flex items-center justify-center">
              <div className="text-center p-4">
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-lg font-medium mb-2">No Video Selected</p>
                <p className="text-sm text-muted-foreground">
                  Upload a video or select one from your library to start analysis.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-0 relative">
        <div className="relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            <video
              ref={videoRef}
              src={video.url}
              className="w-full h-full object-cover rounded-lg"
              preload="metadata"
            />
            {/* Canvas for overlays - properly positioned */}
            <canvas
              ref={canvasRef}
              className="absolute top-0 left-0 w-full h-full pointer-events-none rounded-lg"
              style={{
                width: '100%',
                height: '100%',
                position: 'absolute',
                top: 0,
                left: 0
              }}
            />
          </div>
        </div>
        <div className="p-4 flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" onClick={handleSkipBack}>
                <SkipBack className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={togglePlayPause}>
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              <Button variant="ghost" size="icon" onClick={handleSkipForward}>
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max={duration || 100}
            value={currentTime}
            onChange={handleSeek}
            className="w-full"
          />
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Live Analysis: {liveVehicles.length} vehicles</span>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowOverlay(!showOverlay)}
              >
                {showOverlay ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showOverlay ? 'Hide' : 'Show'} Overlay
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLiveAnalysis(!liveAnalysis)}
              >
                <Activity className="h-4 w-4 mr-1" />
                {liveAnalysis ? 'Stop' : 'Start'} Analysis
              </Button>
            </div>
          </div>
          {liveAnalysis && (
            <div className="grid grid-cols-3 gap-2 text-xs">
              <Badge variant="outline">
                Vehicles: {liveVehicles.length}
              </Badge>
              <Badge variant={speedViolations > 0 ? "destructive" : "secondary"}>
                Violations: {speedViolations}
              </Badge>
              <Badge variant="outline">
                Total: {totalVehicleCount}
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
