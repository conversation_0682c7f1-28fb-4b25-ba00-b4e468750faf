"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Play, Pause, SkipBack, SkipForward } from "lucide-react"

export function VideoPlayer() {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  // Mock vehicle data for demonstration
  const vehicles = [
    { id: 1, x: 100, y: 200, width: 80, height: 40, speed: 75, type: "car", status: "normal" },
    { id: 2, x: 300, y: 150, width: 80, height: 40, speed: 95, type: "car", status: "high" },
    { id: 3, x: 500, y: 250, width: 100, height: 50, speed: 15, type: "truck", status: "low" },
  ]

  useEffect(() => {
    const video = videoRef.current
    if (!video) return

    const handleTimeUpdate = () => {
      setCurrentTime(video.currentTime)
    }

    const handleLoadedMetadata = () => {
      setDuration(video.duration)
    }

    video.addEventListener("timeupdate", handleTimeUpdate)
    video.addEventListener("loadedmetadata", handleLoadedMetadata)

    return () => {
      video.removeEventListener("timeupdate", handleTimeUpdate)
      video.removeEventListener("loadedmetadata", handleLoadedMetadata)
    }
  }, [])

  useEffect(() => {
    const drawOverlay = () => {
      const canvas = canvasRef.current
      const video = videoRef.current
      if (!canvas || !video) return

      const ctx = canvas.getContext("2d")
      if (!ctx) return

      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw bounding boxes for vehicles
      vehicles.forEach((vehicle) => {
        // Set color based on speed status
        let color = "#22c55e" // green for normal
        if (vehicle.status === "high") color = "#ef4444" // red for high speed
        if (vehicle.status === "low") color = "#3b82f6" // blue for low speed

        // Draw bounding box
        ctx.strokeStyle = color
        ctx.lineWidth = 2
        ctx.strokeRect(vehicle.x, vehicle.y, vehicle.width, vehicle.height)

        // Draw speed label
        ctx.fillStyle = color
        ctx.fillRect(vehicle.x, vehicle.y - 20, 60, 20)
        ctx.fillStyle = "white"
        ctx.font = "12px Arial"
        ctx.fillText(`${vehicle.speed} km/h`, vehicle.x + 5, vehicle.y - 5)
      })

      // Request next frame
      if (isPlaying) {
        requestAnimationFrame(drawOverlay)
      }
    }

    if (isPlaying) {
      drawOverlay()
    }
  }, [isPlaying, vehicles])

  const togglePlayPause = () => {
    // Simulate play/pause behavior
    setIsPlaying(!isPlaying)
  }

  const handleSkipBack = () => {
    // Simulate skipping back
    setCurrentTime(Math.max(0, currentTime - 10))
  }

  const handleSkipForward = () => {
    // Simulate skipping forward
    setCurrentTime(Math.min(duration, currentTime + 10))
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
  }

  useEffect(() => {
    let interval: NodeJS.Timeout

    if (isPlaying) {
      interval = setInterval(() => {
        setCurrentTime((prev) => {
          if (prev >= duration) {
            setIsPlaying(false)
            return duration
          }
          return prev + 0.1
        })
      }, 100)
    }

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [isPlaying, duration])

  return (
    <Card>
      <CardContent className="p-0 relative">
        <div className="relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            {/* Use a placeholder div instead of trying to load a non-existent video */}
            <div
              className="absolute inset-0 bg-muted flex items-center justify-center"
              ref={videoRef as React.RefObject<HTMLDivElement>}
            >
              <div className="text-center p-4">
                <p className="text-lg font-medium mb-2">Video Placeholder</p>
                <p className="text-sm text-muted-foreground">
                  This is a demo interface. In a real application, a video would be displayed here.
                </p>
              </div>
            </div>
            {/* Canvas for overlays still works */}
            <canvas ref={canvasRef} className="absolute top-0 left-0 w-full h-full" width={854} height={480} />
          </div>
        </div>
        <div className="p-4 flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" onClick={handleSkipBack}>
                <SkipBack className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={togglePlayPause}>
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              <Button variant="ghost" size="icon" onClick={handleSkipForward}>
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max={duration || 100}
            value={currentTime}
            onChange={(e) => {
              if (videoRef.current) {
                videoRef.current.currentTime = Number(e.target.value)
              }
            }}
            className="w-full"
          />
        </div>
      </CardContent>
    </Card>
  )
}
