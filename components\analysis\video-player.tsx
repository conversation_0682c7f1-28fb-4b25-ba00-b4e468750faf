"use client"

import type React from "react"
import { useState, useRef, useEffect } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Play, Pause, SkipBack, SkipForward, Upload, Eye, EyeOff, Activity } from "lucide-react"
import { videoStore, type UploadedVideo } from "@/lib/video-store"
import { useSearchParams } from "next/navigation"
import { useToast } from "@/hooks/use-toast"

interface VideoPlayerProps {
  videoId?: string
}

// Live vehicle detection data
interface LiveVehicle {
  id: string
  x: number
  y: number
  width: number
  height: number
  speed: number
  type: 'car' | 'truck' | 'bus' | 'motorcycle'
  color: string
  confidence: number
  direction: 'left' | 'right' | 'up' | 'down'
  lane: number
}

export function VideoPlayer({ videoId }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [video, setVideo] = useState<UploadedVideo | null>(null)
  const [liveVehicles, setLiveVehicles] = useState<LiveVehicle[]>([])
  const [showOverlay, setShowOverlay] = useState(true)
  const [liveAnalysis, setLiveAnalysis] = useState(true)
  const [totalVehicleCount, setTotalVehicleCount] = useState(0)
  const [speedViolations, setSpeedViolations] = useState(0)
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const animationRef = useRef<number>(0)
  const vehicleIdCounter = useRef(0)
  const searchParams = useSearchParams()
  const { toast } = useToast()

  // Get video ID from props or URL params
  const currentVideoId = videoId || (searchParams ? searchParams.get('id') : null)

  // Generate slower, more visible live vehicle data
  const generateLiveVehicles = (time: number): LiveVehicle[] => {
    const vehicles: LiveVehicle[] = []
    const numVehicles = 5 + Math.floor(Math.sin(time * 0.1) * 2) // 3-7 vehicles, slowly changing

    for (let i = 0; i < numVehicles; i++) {
      const vehicleType = ['car', 'truck', 'bus', 'motorcycle'][i % 4] as LiveVehicle['type']

      // Create much slower, more stable movement patterns
      const lane = i % 3 // Only 3 lanes for better visibility
      const direction = i % 2 === 0 ? 'right' : 'left' // Alternate directions
      const speed = 35 + (i * 8) + Math.sin(time * 0.05 + i) * 10 // 25-75 km/h, very slow changes

      // Much slower movement - divide by 10 for better visibility
      const pixelsPerSecond = speed * 0.3 // Much slower movement
      const baseX = direction === 'right' ? 50 : 750
      const x = direction === 'right'
        ? baseX + (time * pixelsPerSecond) % 600
        : baseX - (time * pixelsPerSecond) % 600

      // More stable lane positioning
      const y = 120 + lane * 90 + Math.sin(time * 0.02 + i) * 3 // Very slight lane movement

      // Determine color based on speed with clear thresholds
      let color = '#22c55e' // Green for normal (35-55)
      if (speed > 65) color = '#ef4444' // Red for high speed
      else if (speed > 50) color = '#f59e0b' // Orange for moderate high
      else if (speed < 40) color = '#3b82f6' // Blue for slow

      // Keep vehicles on screen longer and more stable
      if (x > 0 && x < 800 && y > 80 && y < 400) {
        vehicles.push({
          id: `vehicle-${i}`, // Stable IDs for consistent tracking
          x: Math.round(x),
          y: Math.round(y),
          width: vehicleType === 'truck' || vehicleType === 'bus' ? 120 : 80,
          height: vehicleType === 'truck' || vehicleType === 'bus' ? 60 : 40,
          speed: Math.round(speed),
          type: vehicleType,
          color,
          confidence: 0.85 + Math.sin(time * 0.1 + i) * 0.1, // Slowly changing confidence
          direction: direction as 'left' | 'right',
          lane: lane
        })
      }
    }

    return vehicles
  }

  useEffect(() => {
    if (currentVideoId) {
      const foundVideo = videoStore.getVideo(currentVideoId)
      setVideo(foundVideo || null)
    }
  }, [currentVideoId])

  // Slower live analysis animation loop for better visibility
  useEffect(() => {
    if (!liveAnalysis || !video) return

    let lastUpdateTime = 0
    const updateInterval = 200 // Update every 200ms instead of every frame for slower, more visible changes

    const animate = (timestamp: number) => {
      if (timestamp - lastUpdateTime >= updateInterval) {
        const time = currentTime
        const newVehicles = generateLiveVehicles(time)
        setLiveVehicles(newVehicles)

        // Update statistics
        setTotalVehicleCount(prev => Math.max(prev, newVehicles.length))
        setSpeedViolations(newVehicles.filter(v => v.speed > 65).length) // Lower threshold for more visible violations

        lastUpdateTime = timestamp
      }

      if (isPlaying && liveAnalysis) {
        animationRef.current = requestAnimationFrame(animate)
      }
    }

    if (isPlaying) {
      animationRef.current = requestAnimationFrame(animate)
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [isPlaying, currentTime, liveAnalysis, video])

  useEffect(() => {
    const videoElement = videoRef.current
    if (!videoElement || !video) return

    const handleLoadedMetadata = () => {
      setDuration(videoElement.duration)
    }

    const handleTimeUpdate = () => {
      setCurrentTime(videoElement.currentTime)
    }

    const handleEnded = () => {
      setIsPlaying(false)
    }

    const handlePlay = () => {
      setIsPlaying(true)
    }

    const handlePause = () => {
      setIsPlaying(false)
    }

    videoElement.addEventListener('loadedmetadata', handleLoadedMetadata)
    videoElement.addEventListener('timeupdate', handleTimeUpdate)
    videoElement.addEventListener('ended', handleEnded)
    videoElement.addEventListener('play', handlePlay)
    videoElement.addEventListener('pause', handlePause)

    return () => {
      videoElement.removeEventListener('loadedmetadata', handleLoadedMetadata)
      videoElement.removeEventListener('timeupdate', handleTimeUpdate)
      videoElement.removeEventListener('ended', handleEnded)
      videoElement.removeEventListener('play', handlePlay)
      videoElement.removeEventListener('pause', handlePause)
    }
  }, [video])

  // Enhanced live analysis drawing system with better visibility
  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas || !showOverlay) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    const drawLiveAnalysis = () => {
      // Clear canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // Draw live vehicles with much more prominent colored overlays
      liveVehicles.forEach((vehicle) => {
        const borderColor = vehicle.color
        const fillColor = vehicle.color + '40' // More visible transparency

        // Draw thick, prominent vehicle bounding box
        ctx.strokeStyle = borderColor
        ctx.fillStyle = fillColor
        ctx.lineWidth = 5 // Thicker border for better visibility
        ctx.lineCap = "round"
        ctx.lineJoin = "round"

        // More prominent filled rectangle
        ctx.fillRect(vehicle.x, vehicle.y, vehicle.width, vehicle.height)

        // Thick border rectangle
        ctx.strokeRect(vehicle.x, vehicle.y, vehicle.width, vehicle.height)

        // Larger, more prominent speed indicator
        const speedBoxWidth = 100
        const speedBoxHeight = 30
        ctx.fillStyle = borderColor
        ctx.fillRect(vehicle.x - 5, vehicle.y - speedBoxHeight - 8, speedBoxWidth, speedBoxHeight)

        // Add border to speed box
        ctx.strokeStyle = "white"
        ctx.lineWidth = 2
        ctx.strokeRect(vehicle.x - 5, vehicle.y - speedBoxHeight - 8, speedBoxWidth, speedBoxHeight)

        // Larger speed text
        ctx.fillStyle = "white"
        ctx.font = "bold 16px Arial"
        ctx.textAlign = "left"
        ctx.fillText(`${vehicle.speed} km/h`, vehicle.x + 2, vehicle.y - 15)

        // Larger vehicle type label
        const typeBoxWidth = 90
        const typeBoxHeight = 25
        ctx.fillStyle = borderColor
        ctx.fillRect(vehicle.x - 5, vehicle.y + vehicle.height + 5, typeBoxWidth, typeBoxHeight)

        // Add border to type box
        ctx.strokeStyle = "white"
        ctx.lineWidth = 2
        ctx.strokeRect(vehicle.x - 5, vehicle.y + vehicle.height + 5, typeBoxWidth, typeBoxHeight)

        ctx.fillStyle = "white"
        ctx.font = "bold 13px Arial"
        ctx.fillText(`${vehicle.type.toUpperCase()}`, vehicle.x + 2, vehicle.y + vehicle.height + 22)

        // More prominent speed violation indicator
        if (vehicle.speed > 65) {
          ctx.fillStyle = "#dc2626"
          ctx.fillRect(vehicle.x + vehicle.width + 10, vehicle.y, 120, 25)
          ctx.strokeStyle = "white"
          ctx.lineWidth = 2
          ctx.strokeRect(vehicle.x + vehicle.width + 10, vehicle.y, 120, 25)

          ctx.fillStyle = "white"
          ctx.font = "bold 14px Arial"
          ctx.fillText("⚠ VIOLATION", vehicle.x + vehicle.width + 15, vehicle.y + 17)
        }

        // Thicker confidence indicator bar
        const confidenceWidth = vehicle.width * vehicle.confidence
        ctx.fillStyle = "rgba(34, 197, 94, 1)"
        ctx.fillRect(vehicle.x, vehicle.y - 8, confidenceWidth, 6)

        // Larger direction arrow
        ctx.fillStyle = borderColor
        ctx.font = "bold 24px Arial"
        const arrow = vehicle.direction === 'right' ? '→' : '←'
        ctx.fillText(arrow, vehicle.x + vehicle.width/2 - 12, vehicle.y + vehicle.height/2 + 8)

        // Add vehicle ID for tracking
        ctx.fillStyle = "white"
        ctx.font = "bold 12px Arial"
        ctx.fillText(`ID: ${vehicle.id.split('-')[1]}`, vehicle.x + 5, vehicle.y + 15)
      })

      // Enhanced live analysis statistics overlay
      ctx.fillStyle = "rgba(0, 0, 0, 0.9)"
      ctx.fillRect(10, 10, 280, 140)

      // Add border to stats box
      ctx.strokeStyle = "#22c55e"
      ctx.lineWidth = 3
      ctx.strokeRect(10, 10, 280, 140)

      ctx.fillStyle = "white"
      ctx.font = "bold 18px Arial"
      ctx.fillText("🔴 LIVE TRAFFIC ANALYSIS", 20, 35)

      ctx.font = "16px Arial"
      ctx.fillText(`Vehicles Detected: ${liveVehicles.length}`, 20, 60)

      const avgSpeed = liveVehicles.length > 0
        ? Math.round(liveVehicles.reduce((sum, v) => sum + v.speed, 0) / liveVehicles.length)
        : 0
      ctx.fillText(`Average Speed: ${avgSpeed} km/h`, 20, 85)

      const violations = liveVehicles.filter(v => v.speed > 65).length
      ctx.fillStyle = violations > 0 ? "#ef4444" : "#22c55e"
      ctx.fillText(`Speed Violations: ${violations}`, 20, 110)

      ctx.fillStyle = "white"
      ctx.font = "14px Arial"
      ctx.fillText(`Time: ${formatTime(currentTime)}`, 20, 135)

      // Add color legend
      ctx.fillStyle = "rgba(0, 0, 0, 0.8)"
      ctx.fillRect(canvas.width - 200, 10, 180, 100)
      ctx.strokeStyle = "white"
      ctx.lineWidth = 2
      ctx.strokeRect(canvas.width - 200, 10, 180, 100)

      ctx.fillStyle = "white"
      ctx.font = "bold 14px Arial"
      ctx.fillText("Speed Legend:", canvas.width - 190, 30)

      // Color legend items
      const legendItems = [
        { color: '#3b82f6', text: 'Slow (<40 km/h)' },
        { color: '#22c55e', text: 'Normal (40-50)' },
        { color: '#f59e0b', text: 'Fast (50-65)' },
        { color: '#ef4444', text: 'Violation (65+)' }
      ]

      legendItems.forEach((item, i) => {
        ctx.fillStyle = item.color
        ctx.fillRect(canvas.width - 190, 40 + i * 15, 15, 10)
        ctx.fillStyle = "white"
        ctx.font = "12px Arial"
        ctx.fillText(item.text, canvas.width - 170, 49 + i * 15)
      })
    }

    drawLiveAnalysis()
  }, [liveVehicles, showOverlay, currentTime])

  const togglePlayPause = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    if (isPlaying) {
      videoElement.pause()
    } else {
      videoElement.play()
    }
  }

  const handleSkipBack = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.currentTime = Math.max(0, videoElement.currentTime - 10)
  }

  const handleSkipForward = () => {
    const videoElement = videoRef.current
    if (!videoElement) return

    videoElement.currentTime = Math.min(videoElement.duration, videoElement.currentTime + 10)
  }

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60)
    const seconds = Math.floor(time % 60)
    return `${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`
  }

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const videoElement = videoRef.current
    if (!videoElement) return

    const newTime = Number(e.target.value)
    videoElement.currentTime = newTime
    setCurrentTime(newTime)
  }

  if (!video) {
    return (
      <Card>
        <CardContent className="p-0 relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            <div className="absolute inset-0 bg-muted flex items-center justify-center">
              <div className="text-center p-4">
                <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <p className="text-lg font-medium mb-2">No Video Selected</p>
                <p className="text-sm text-muted-foreground">
                  Upload a video or select one from your library to start analysis.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-0 relative">
        <div className="relative">
          <div className="relative w-full" style={{ aspectRatio: "16/9" }}>
            <video
              ref={videoRef}
              src={video.url}
              className="w-full h-full object-cover"
              preload="metadata"
            />
            {/* Canvas for overlays */}
            <canvas
              ref={canvasRef}
              className="absolute top-0 left-0 w-full h-full pointer-events-none"
              width={854}
              height={480}
            />
          </div>
        </div>
        <div className="p-4 flex flex-col gap-2">
          <div className="flex items-center justify-between">
            <span className="text-sm">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="icon" onClick={handleSkipBack}>
                <SkipBack className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="icon" onClick={togglePlayPause}>
                {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </Button>
              <Button variant="ghost" size="icon" onClick={handleSkipForward}>
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <input
            type="range"
            min="0"
            max={duration || 100}
            value={currentTime}
            onChange={handleSeek}
            className="w-full"
          />
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Live Analysis: {liveVehicles.length} vehicles</span>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowOverlay(!showOverlay)}
              >
                {showOverlay ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showOverlay ? 'Hide' : 'Show'} Overlay
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLiveAnalysis(!liveAnalysis)}
              >
                <Activity className="h-4 w-4 mr-1" />
                {liveAnalysis ? 'Stop' : 'Start'} Analysis
              </Button>
            </div>
          </div>
          {liveAnalysis && (
            <div className="grid grid-cols-3 gap-2 text-xs">
              <Badge variant="outline">
                Vehicles: {liveVehicles.length}
              </Badge>
              <Badge variant={speedViolations > 0 ? "destructive" : "secondary"}>
                Violations: {speedViolations}
              </Badge>
              <Badge variant="outline">
                Total: {totalVehicleCount}
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
