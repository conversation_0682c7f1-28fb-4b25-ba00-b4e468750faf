"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, ArrowDown } from "lucide-react"

export function SpeedThresholdControls() {
  const [highSpeedThreshold, setHighSpeedThreshold] = useState(80)
  const [lowSpeedThreshold, setLowSpeedThreshold] = useState(20)

  return (
    <Card>
      <CardHeader>
        <CardTitle>Speed Thresholds</CardTitle>
        <CardDescription>Adjust thresholds for speed classification</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
              <span>High Speed Threshold</span>
            </div>
            <Badge variant="destructive">{highSpeedThreshold} km/h</Badge>
          </div>
          <Slider
            value={[highSpeedThreshold]}
            min={50}
            max={120}
            step={5}
            onValueChange={(value) => setHighSpeedThreshold(value[0])}
          />
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <ArrowDown className="h-4 w-4 text-blue-500 mr-2" />
              <span>Low Speed Threshold</span>
            </div>
            <Badge variant="secondary">{lowSpeedThreshold} km/h</Badge>
          </div>
          <Slider
            value={[lowSpeedThreshold]}
            min={5}
            max={40}
            step={5}
            onValueChange={(value) => setLowSpeedThreshold(value[0])}
          />
        </div>

        <Button className="w-full">Apply Thresholds</Button>
      </CardContent>
    </Card>
  )
}
