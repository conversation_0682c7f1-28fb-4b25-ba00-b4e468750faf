"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>ontainer, ChartTooltipContent } from "@/components/ui/chart"
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from "recharts"

export function SpeedDistributionChart() {
  const data = [
    { speed: "0-20", count: 45, color: "#3b82f6" },
    { speed: "21-40", count: 132, color: "#3b82f6" },
    { speed: "41-60", count: 341, color: "#3b82f6" },
    { speed: "61-80", count: 132, color: "#3b82f6" },
    { speed: "81-100", count: 52, color: "#ef4444" },
    { speed: "100+", count: 12, color: "#ef4444" },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Speed Distribution</CardTitle>
        <CardDescription>Vehicle speed distribution (km/h)</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            count: {
              label: "Vehicle Count",
              color: "hsl(var(--chart-1))",
            },
          }}
          className="h-[300px]"
        >
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="speed" />
              <YAxis />
              <Tooltip content={<ChartTooltipContent />} />
              <Bar dataKey="count" fill="var(--color-count)">
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
