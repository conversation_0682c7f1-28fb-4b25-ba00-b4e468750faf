"use client"

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltipContent } from "@/components/ui/chart"
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"
import { cn } from "@/lib/utils"

interface BatchSummaryProps {
  className?: string
}

export function BatchSummary({ className }: BatchSummaryProps) {
  // Mock data for the trend chart
  const trendData = [
    { date: "2023-06-01", total: 120, highSpeed: 8, lowSpeed: 6 },
    { date: "2023-06-02", total: 145, highSpeed: 12, lowSpeed: 8 },
    { date: "2023-06-03", total: 132, highSpeed: 10, lowSpeed: 7 },
    { date: "2023-06-04", total: 158, highSpeed: 15, lowSpeed: 9 },
    { date: "2023-06-05", total: 142, highSpeed: 11, lowSpeed: 5 },
    { date: "2023-06-06", total: 165, highSpeed: 18, lowSpeed: 10 },
    { date: "2023-06-07", total: 172, highSpeed: 14, lowSpeed: 8 },
  ]

  return (
    <Card className={cn(className)}>
      <CardHeader>
        <CardTitle>Traffic Trends</CardTitle>
        <CardDescription>Vehicle count and speed violations over time</CardDescription>
      </CardHeader>
      <CardContent>
        <ChartContainer
          config={{
            total: {
              label: "Total Vehicles",
              color: "hsl(var(--chart-1))",
            },
            highSpeed: {
              label: "High Speed",
              color: "hsl(var(--chart-2))",
            },
            lowSpeed: {
              label: "Low Speed",
              color: "hsl(var(--chart-3))",
            },
          }}
          className="h-[300px]"
        >
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={trendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip content={<ChartTooltipContent />} />
              <Legend />
              <Line type="monotone" dataKey="total" stroke="var(--color-total)" />
              <Line type="monotone" dataKey="highSpeed" stroke="var(--color-highSpeed)" />
              <Line type="monotone" dataKey="lowSpeed" stroke="var(--color-lowSpeed)" />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
