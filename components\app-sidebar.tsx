"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>bar<PERSON>ontent,
  SidebarHeader,
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { ModeToggle } from "@/components/mode-toggle"
import { BarChart3, Settings, Upload, Video, Layers, User, Activity } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { VideoUploadDialog } from "@/components/video-upload-dialog"
import { videoStore, type UploadedVideo } from "@/lib/video-store"
import { useToast } from "@/hooks/use-toast"

export function AppSidebar() {
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false)
  const { toast } = useToast()

  const handleVideoUploaded = (video: UploadedVideo) => {
    videoStore.addVideo(video)
    toast({
      title: "Video uploaded successfully",
      description: `${video.name} has been uploaded and is ready for analysis.`,
    })
  }

  return (
    <>
      <Sidebar>
        <SidebarHeader className="flex items-center justify-between">
          <div className="flex items-center gap-2 px-2">
            <Video className="h-6 w-6" />
            <h1 className="text-lg font-bold">Traffic Analysis</h1>
          </div>
          <SidebarTrigger />
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>Navigation</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild tooltip="Dashboard">
                    <Link href="/">
                      <BarChart3 />
                      <span>Dashboard</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild tooltip="Real-time Analysis">
                    <Link href="/realtime">
                      <Activity />
                      <span>Real-time Analysis</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild tooltip="Video Analysis">
                    <Link href="/analysis">
                      <Video />
                      <span>Video Analysis</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild tooltip="Batch Results">
                    <Link href="/batch">
                      <Layers />
                      <span>Batch Results</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild tooltip="Settings">
                    <Link href="/settings">
                      <Settings />
                      <span>Settings</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
          <SidebarGroup>
            <SidebarGroupLabel>Actions</SidebarGroupLabel>
            <SidebarGroupContent>
              <Button
                className="w-full"
                size="sm"
                onClick={() => setUploadDialogOpen(true)}
              >
                <Upload className="mr-2 h-4 w-4" />
                Upload Video
              </Button>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter className="flex items-center justify-between p-4">
          <div className="flex items-center gap-2">
            <User className="h-6 w-6" />
            <span>Admin</span>
          </div>
          <ModeToggle />
        </SidebarFooter>
      </Sidebar>

      <VideoUploadDialog
        open={uploadDialogOpen}
        onOpenChange={setUploadDialogOpen}
        onVideoUploaded={handleVideoUploaded}
      />
    </>
  )
}
