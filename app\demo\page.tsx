"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { VideoPlayer } from "@/components/analysis/video-player"
import { videoStore, type UploadedVideo } from "@/lib/video-store"
import { useToast } from "@/hooks/use-toast"
import { Play, Upload, Zap } from "lucide-react"

export default function DemoPage() {
  const [demoVideo, setDemoVideo] = useState<UploadedVideo | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    // Create a demo video entry for testing
    const createDemoVideo = () => {
      const demo: UploadedVideo = {
        id: "demo-video-123",
        name: "Traffic Demo Video.mp4",
        size: 15728640, // 15MB
        duration: 120, // 2 minutes
        url: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4", // Sample video
        uploadedAt: new Date(),
        analyzed: true,
        analysisResults: {
          id: "demo-analysis",
          videoId: "demo-video-123",
          totalVehicles: 45,
          averageSpeed: 52.3,
          speedViolations: 8,
          vehicleTypes: {
            car: 32,
            truck: 8,
            bus: 3,
            motorcycle: 2
          },
          vehicles: [],
          processingTime: 2.1,
          analyzedAt: new Date()
        }
      }
      
      setDemoVideo(demo)
      
      // Add to video store for testing
      videoStore.addVideo(demo)
    }

    createDemoVideo()
  }, [])

  const handleStartDemo = () => {
    toast({
      title: "🎬 Live Analysis Demo Started",
      description: "Watch the real-time vehicle detection with colored overlays and speed tracking!",
    })
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-6 w-6 text-yellow-500" />
            Live Video Analysis Demo
          </CardTitle>
          <CardDescription>
            Experience real-time traffic analysis with colored vehicle detection and live speed tracking
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-blue-500 rounded"></div>
              <span className="text-sm">Slow (20-35 km/h)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span className="text-sm">Normal (35-50 km/h)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-orange-500 rounded"></div>
              <span className="text-sm">Fast (50-65 km/h)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span className="text-sm">Violation (65+ km/h)</span>
            </div>
          </div>

          <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border-l-4 border-blue-500">
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">🎯 NEW: Realistic Vehicle Movement & Speed Tracking</h4>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Each vehicle now has <strong>realistic movement patterns</strong> and <strong>colored speed bars</strong>:
            </p>
            <ul className="text-sm text-blue-700 dark:text-blue-300 mt-2 space-y-1">
              <li>• <strong>Vehicle-specific behavior</strong> - Cars are agile, trucks are steady, buses stop frequently</li>
              <li>• <strong>Realistic speeds</strong> - Each vehicle type has appropriate speed ranges</li>
              <li>• <strong>Lane changing</strong> - Motorcycles weave, trucks stay in lanes</li>
              <li>• <strong>Speed bars</strong> that match vehicle size and change color in real-time</li>
              <li>• <strong>Acceleration patterns</strong> - Different vehicles accelerate/brake differently</li>
            </ul>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
            <Badge variant="outline">🚗 Cars</Badge>
            <Badge variant="outline">🚛 Trucks</Badge>
            <Badge variant="outline">🚌 Buses</Badge>
            <Badge variant="outline">🏍️ Motorcycles</Badge>
          </div>

          <Button onClick={handleStartDemo} className="w-full">
            <Play className="mr-2 h-4 w-4" />
            Start Live Analysis Demo
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Live Analysis Features</CardTitle>
          <CardDescription>
            Real-time vehicle detection and speed analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium">🚗 Realistic Vehicle Behavior:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• <strong>Cars</strong>: Agile movement, frequent speed changes (30-60 km/h)</li>
                <li>• <strong>Trucks</strong>: Steady movement, gradual speed changes (25-45 km/h)</li>
                <li>• <strong>Buses</strong>: Stop-and-go patterns, frequent stops (22-38 km/h)</li>
                <li>• <strong>Motorcycles</strong>: Weaving movement, quick acceleration (30-70 km/h)</li>
                <li>• <strong>Lane changing</strong>: Realistic lane change patterns</li>
                <li>• <strong>Speed tracking bars</strong> sized to match each vehicle</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">📊 Live Analysis Features:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Real-time speed bars directly on vehicles</li>
                <li>• Color-coded speed categories with legend</li>
                <li>• Vehicle-specific movement patterns</li>
                <li>• Speed violation alerts with flashing borders</li>
                <li>• Live statistics overlay</li>
                <li>• Proportional sizing for all screen sizes</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {demoVideo && (
        <Card>
          <CardHeader>
            <CardTitle>🎬 Demo Video Player</CardTitle>
            <CardDescription>
              Click play to see live analysis with colored overlays and speed tracking
            </CardDescription>
          </CardHeader>
          <CardContent>
            <VideoPlayer videoId={demoVideo.id} />
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">📋 How to Use the Speed Tracking Bars:</h4>
              <ol className="text-sm text-muted-foreground space-y-1">
                <li>1. Click the play button to start the video</li>
                <li>2. Watch for <strong>colored speed bars directly on each vehicle</strong></li>
                <li>3. Observe how the <strong>bar color changes</strong> as vehicle speed changes</li>
                <li>4. Notice the <strong>bar length</strong> represents speed intensity</li>
                <li>5. See the <strong>speed number displayed on the vehicle</strong></li>
                <li>6. Look for <strong>flashing red borders</strong> on speeding vehicles</li>
                <li>7. Watch <strong>vehicle types</strong> displayed above each vehicle</li>
                <li>8. See <strong>direction arrows</strong> below vehicles</li>
                <li>9. Use "Show/Hide Overlay" to toggle analysis display</li>
                <li>10. Use "Start/Stop Analysis" to control live detection</li>
              </ol>

              <div className="mt-3 p-3 bg-green-50 dark:bg-green-950 rounded border-l-4 border-green-500">
                <h5 className="font-medium text-green-800 dark:text-green-200 mb-1">🚗 Realistic Movement Patterns:</h5>
                <ul className="text-xs text-green-700 dark:text-green-300 space-y-1">
                  <li>• <strong>Cars</strong> - Quick acceleration, frequent lane changes, agile movement</li>
                  <li>• <strong>Trucks</strong> - Steady speeds, gradual changes, stay in lanes</li>
                  <li>• <strong>Buses</strong> - Stop-and-go patterns, frequent braking for stops</li>
                  <li>• <strong>Motorcycles</strong> - Weaving through traffic, quick speed changes</li>
                  <li>• <strong>Speed bars</strong> - Perfectly sized to match each vehicle</li>
                  <li>• <strong>Real-time tracking</strong> - Colors and lengths change with speed</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>🚀 Upload Your Own Video</CardTitle>
          <CardDescription>
            Test the live analysis with your own traffic videos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg font-medium mb-2">Ready to analyze your videos?</p>
            <p className="text-sm text-muted-foreground mb-4">
              Go to the main dashboard to upload and analyze your own traffic videos
            </p>
            <Button asChild>
              <a href="/">Go to Dashboard</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
