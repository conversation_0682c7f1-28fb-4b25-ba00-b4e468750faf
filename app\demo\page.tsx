"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { VideoPlayer } from "@/components/analysis/video-player"
import { videoStore, type UploadedVideo } from "@/lib/video-store"
import { useToast } from "@/hooks/use-toast"
import { Play, Upload, Zap } from "lucide-react"

export default function DemoPage() {
  const [demoVideo, setDemoVideo] = useState<UploadedVideo | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    // Create a demo video entry for testing
    const createDemoVideo = () => {
      const demo: UploadedVideo = {
        id: "demo-video-123",
        name: "Traffic Demo Video.mp4",
        size: 15728640, // 15MB
        duration: 120, // 2 minutes
        url: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4", // Sample video
        uploadedAt: new Date(),
        analyzed: true,
        analysisResults: {
          id: "demo-analysis",
          videoId: "demo-video-123",
          totalVehicles: 45,
          averageSpeed: 52.3,
          speedViolations: 8,
          vehicleTypes: {
            car: 32,
            truck: 8,
            bus: 3,
            motorcycle: 2
          },
          vehicles: [],
          processingTime: 2.1,
          analyzedAt: new Date()
        }
      }
      
      setDemoVideo(demo)
      
      // Add to video store for testing
      videoStore.addVideo(demo)
    }

    createDemoVideo()
  }, [])

  const handleStartDemo = () => {
    toast({
      title: "🎬 Live Analysis Demo Started",
      description: "Watch the real-time vehicle detection with colored overlays and speed tracking!",
    })
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-6 w-6 text-yellow-500" />
            Live Video Analysis Demo
          </CardTitle>
          <CardDescription>
            Experience real-time traffic analysis with colored vehicle detection and live speed tracking
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-blue-500 rounded"></div>
              <span className="text-sm">Slow (20-35 km/h)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-500 rounded"></div>
              <span className="text-sm">Normal (35-50 km/h)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-orange-500 rounded"></div>
              <span className="text-sm">Fast (50-65 km/h)</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-500 rounded"></div>
              <span className="text-sm">Violation (65+ km/h)</span>
            </div>
          </div>

          <div className="p-4 bg-blue-50 dark:bg-blue-950 rounded-lg border-l-4 border-blue-500">
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">🎯 NEW: Colored Speed Tracking Bars</h4>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Each vehicle now has a <strong>colored speed bar directly on the vehicle</strong> that:
            </p>
            <ul className="text-sm text-blue-700 dark:text-blue-300 mt-2 space-y-1">
              <li>• <strong>Changes color</strong> in real-time as speed changes</li>
              <li>• <strong>Bar length</strong> represents speed intensity (longer = faster)</li>
              <li>• <strong>Speed number</strong> displayed directly on the vehicle</li>
              <li>• <strong>Flashing red border</strong> for speed violations</li>
            </ul>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-2">
            <Badge variant="outline">🚗 Cars</Badge>
            <Badge variant="outline">🚛 Trucks</Badge>
            <Badge variant="outline">🚌 Buses</Badge>
            <Badge variant="outline">🏍️ Motorcycles</Badge>
          </div>

          <Button onClick={handleStartDemo} className="w-full">
            <Play className="mr-2 h-4 w-4" />
            Start Live Analysis Demo
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Live Analysis Features</CardTitle>
          <CardDescription>
            Real-time vehicle detection and speed analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium">✨ Visual Features:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Colored bounding boxes for each vehicle</li>
                <li>• Real-time speed display above each vehicle</li>
                <li>• Vehicle type labels (Car, Truck, Bus, Motorcycle)</li>
                <li>• Direction arrows showing movement</li>
                <li>• Speed violation warnings</li>
                <li>• Confidence indicators</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">📊 Live Statistics:</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Total vehicles detected</li>
                <li>• Average speed calculation</li>
                <li>• Speed violation count</li>
                <li>• Real-time timestamp</li>
                <li>• Vehicle type distribution</li>
                <li>• Analysis confidence levels</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {demoVideo && (
        <Card>
          <CardHeader>
            <CardTitle>🎬 Demo Video Player</CardTitle>
            <CardDescription>
              Click play to see live analysis with colored overlays and speed tracking
            </CardDescription>
          </CardHeader>
          <CardContent>
            <VideoPlayer videoId={demoVideo.id} />
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h4 className="font-medium mb-2">📋 How to Use the Speed Tracking Bars:</h4>
              <ol className="text-sm text-muted-foreground space-y-1">
                <li>1. Click the play button to start the video</li>
                <li>2. Watch for <strong>colored speed bars directly on each vehicle</strong></li>
                <li>3. Observe how the <strong>bar color changes</strong> as vehicle speed changes</li>
                <li>4. Notice the <strong>bar length</strong> represents speed intensity</li>
                <li>5. See the <strong>speed number displayed on the vehicle</strong></li>
                <li>6. Look for <strong>flashing red borders</strong> on speeding vehicles</li>
                <li>7. Watch <strong>vehicle types</strong> displayed above each vehicle</li>
                <li>8. See <strong>direction arrows</strong> below vehicles</li>
                <li>9. Use "Show/Hide Overlay" to toggle analysis display</li>
                <li>10. Use "Start/Stop Analysis" to control live detection</li>
              </ol>

              <div className="mt-3 p-3 bg-green-50 dark:bg-green-950 rounded border-l-4 border-green-500">
                <h5 className="font-medium text-green-800 dark:text-green-200 mb-1">🎯 Speed Tracking Features:</h5>
                <ul className="text-xs text-green-700 dark:text-green-300 space-y-1">
                  <li>• <strong>Real-time speed bars</strong> - Colored bars directly on vehicles</li>
                  <li>• <strong>Dynamic colors</strong> - Colors change as speed changes</li>
                  <li>• <strong>Speed numbers</strong> - Large speed display on each vehicle</li>
                  <li>• <strong>Violation alerts</strong> - Flashing red borders for speeding</li>
                  <li>• <strong>Bar length</strong> - Longer bars = higher speeds</li>
                  <li>• <strong>Smooth tracking</strong> - Bars update in real-time</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>🚀 Upload Your Own Video</CardTitle>
          <CardDescription>
            Test the live analysis with your own traffic videos
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <p className="text-lg font-medium mb-2">Ready to analyze your videos?</p>
            <p className="text-sm text-muted-foreground mb-4">
              Go to the main dashboard to upload and analyze your own traffic videos
            </p>
            <Button asChild>
              <a href="/">Go to Dashboard</a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
