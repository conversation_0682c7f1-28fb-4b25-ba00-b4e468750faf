"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  AlertTriangle, 
  X, 
  Bell, 
  BellOff, 
  Filter,
  Clock,
  MapPin,
  Car
} from "lucide-react"
import { realtimeAnalysis, type RealtimeAlert } from "@/lib/realtime-analysis"

export function RealtimeAlerts() {
  const [alerts, setAlerts] = useState<RealtimeAlert[]>([])
  const [filter, setFilter] = useState<'all' | 'unresolved' | 'critical' | 'high'>('all')
  const [soundEnabled, setSoundEnabled] = useState(true)

  useEffect(() => {
    const unsubscribe = realtimeAnalysis.subscribeToAlerts(setAlerts)
    return unsubscribe
  }, [])

  const handleResolveAlert = (alertId: string) => {
    realtimeAnalysis.resolveAlert(alertId)
  }

  const handleClearAll = () => {
    realtimeAnalysis.clearAlerts()
  }

  const getFilteredAlerts = () => {
    switch (filter) {
      case 'unresolved':
        return alerts.filter(alert => !alert.resolved)
      case 'critical':
        return alerts.filter(alert => alert.severity === 'critical' && !alert.resolved)
      case 'high':
        return alerts.filter(alert => (alert.severity === 'high' || alert.severity === 'critical') && !alert.resolved)
      default:
        return alerts
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'speed_violation': return <Car className="h-4 w-4" />
      case 'congestion': return <AlertTriangle className="h-4 w-4" />
      case 'lane_violation': return <AlertTriangle className="h-4 w-4" />
      case 'accident_risk': return <AlertTriangle className="h-4 w-4" />
      default: return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getAlertTypeLabel = (type: string) => {
    switch (type) {
      case 'speed_violation': return 'Speed Violation'
      case 'congestion': return 'Traffic Congestion'
      case 'lane_violation': return 'Lane Violation'
      case 'accident_risk': return 'Accident Risk'
      default: return 'Unknown'
    }
  }

  const filteredAlerts = getFilteredAlerts()
  const unresolvedCount = alerts.filter(alert => !alert.resolved).length
  const criticalCount = alerts.filter(alert => alert.severity === 'critical' && !alert.resolved).length

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Traffic Alerts
              {unresolvedCount > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {unresolvedCount}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Real-time traffic alerts and notifications
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSoundEnabled(!soundEnabled)}
            >
              {soundEnabled ? <Bell className="h-4 w-4" /> : <BellOff className="h-4 w-4" />}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearAll}
              disabled={alerts.length === 0}
            >
              Clear All
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filter Controls */}
        <div className="flex gap-2 mb-4">
          <Button
            variant={filter === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('all')}
          >
            All ({alerts.length})
          </Button>
          <Button
            variant={filter === 'unresolved' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('unresolved')}
          >
            Unresolved ({unresolvedCount})
          </Button>
          <Button
            variant={filter === 'critical' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('critical')}
          >
            Critical ({criticalCount})
          </Button>
          <Button
            variant={filter === 'high' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setFilter('high')}
          >
            High+ ({alerts.filter(a => (a.severity === 'high' || a.severity === 'critical') && !a.resolved).length})
          </Button>
        </div>

        {/* Alerts List */}
        <ScrollArea className="h-96">
          <div className="space-y-3">
            {filteredAlerts.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <BellOff className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No alerts to display</p>
                <p className="text-sm">Alerts will appear here when traffic issues are detected</p>
              </div>
            ) : (
              filteredAlerts.map((alert) => (
                <Alert
                  key={alert.id}
                  className={`border-l-4 ${
                    alert.severity === 'critical' ? 'border-l-red-500' :
                    alert.severity === 'high' ? 'border-l-orange-500' :
                    alert.severity === 'medium' ? 'border-l-yellow-500' :
                    'border-l-blue-500'
                  } ${alert.resolved ? 'opacity-60' : ''}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      {getAlertIcon(alert.type)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <Badge className={getSeverityColor(alert.severity)}>
                            {alert.severity.toUpperCase()}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {getAlertTypeLabel(alert.type)}
                          </Badge>
                          {alert.resolved && (
                            <Badge variant="secondary" className="text-xs">
                              RESOLVED
                            </Badge>
                          )}
                        </div>
                        <AlertDescription className="text-sm">
                          {alert.message}
                        </AlertDescription>
                        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            {alert.timestamp.toLocaleTimeString()}
                          </div>
                          {alert.location && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              ({Math.round(alert.location.x)}, {Math.round(alert.location.y)})
                            </div>
                          )}
                          {alert.vehicleId && (
                            <div className="flex items-center gap-1">
                              <Car className="h-3 w-3" />
                              {alert.vehicleId}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                    {!alert.resolved && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleResolveAlert(alert.id)}
                        className="h-6 w-6 p-0"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </Alert>
              ))
            )}
          </div>
        </ScrollArea>

        {/* Summary */}
        {alerts.length > 0 && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Total alerts: {alerts.length}</span>
              <span>Unresolved: {unresolvedCount}</span>
              <span>Critical: {criticalCount}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 