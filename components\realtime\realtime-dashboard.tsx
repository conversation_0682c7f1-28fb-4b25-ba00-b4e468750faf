"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Activity, 
  Car, 
  Truck, 
  Bus, 
  Bike, 
  AlertTriangle, 
  Play, 
  Pause, 
  RotateCcw,
  TrendingUp,
  Clock,
  Zap
} from "lucide-react"
import { realtimeAnalysis, type RealtimeStats, type RealtimeAlert } from "@/lib/realtime-analysis"

export function RealtimeDashboard() {
  const [stats, setStats] = useState<RealtimeStats>(realtimeAnalysis.getCurrentStats())
  const [alerts, setAlerts] = useState<RealtimeAlert[]>([])
  const [isRunning, setIsRunning] = useState(false)

  useEffect(() => {
    const unsubscribeStats = realtimeAnalysis.subscribe(setStats)
    const unsubscribeAlerts = realtimeAnalysis.subscribeToAlerts(setAlerts)

    return () => {
      unsubscribeStats()
      unsubscribeAlerts()
    }
  }, [])

  const handleStartAnalysis = () => {
    realtimeAnalysis.startAnalysis()
    setIsRunning(true)
  }

  const handleStopAnalysis = () => {
    realtimeAnalysis.stopAnalysis()
    setIsRunning(false)
  }

  const handleReset = () => {
    realtimeAnalysis.stopAnalysis()
    setIsRunning(false)
    // Reset would be implemented in the service
  }

  const getTrafficFlowColor = (flow: string) => {
    switch (flow) {
      case 'low': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'congested': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'low': return 'bg-blue-100 text-blue-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'critical': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getVehicleIcon = (type: string) => {
    switch (type) {
      case 'car': return <Car className="h-4 w-4" />
      case 'truck': return <Truck className="h-4 w-4" />
      case 'bus': return <Bus className="h-4 w-4" />
      case 'motorcycle': return <Bike className="h-4 w-4" />
      default: return <Car className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Control Panel */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Real-time Traffic Analysis
          </CardTitle>
          <CardDescription>
            Live monitoring and analysis of traffic patterns
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button
              onClick={isRunning ? handleStopAnalysis : handleStartAnalysis}
              variant={isRunning ? "destructive" : "default"}
              className="flex items-center gap-2"
            >
              {isRunning ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              {isRunning ? "Stop Analysis" : "Start Analysis"}
            </Button>
            <Button
              onClick={handleReset}
              variant="outline"
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset
            </Button>
            <Badge variant={isRunning ? "default" : "secondary"} className="ml-auto">
              {isRunning ? "LIVE" : "STOPPED"}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Current Vehicles</CardTitle>
            <Car className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.currentVehicles}</div>
            <p className="text-xs text-muted-foreground">
              Total: {stats.totalVehicles}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Speed Violations</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.speedViolations}</div>
            <p className="text-xs text-muted-foreground">
              {stats.currentVehicles > 0 ? Math.round((stats.speedViolations / stats.currentVehicles) * 100) : 0}% of vehicles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Speed</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageSpeed} km/h</div>
            <p className="text-xs text-muted-foreground">
              Speed limit: 60 km/h
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Traffic Flow</CardTitle>
            <Zap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <Badge className={getTrafficFlowColor(stats.trafficFlow)}>
              {stats.trafficFlow.toUpperCase()}
            </Badge>
            <p className="text-xs text-muted-foreground mt-1">
              {stats.congestionLevel}% congestion
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Vehicle Type Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Type Distribution</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {Object.entries(stats.vehicleTypes).map(([type, count]) => (
              <div key={type} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  {getVehicleIcon(type)}
                  <span className="font-medium capitalize">{type}</span>
                </div>
                <Badge variant="secondary">{count}</Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Lane Occupancy */}
      <Card>
        <CardHeader>
          <CardTitle>Lane Occupancy</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(stats.laneOccupancy).map(([lane, count]) => (
              <div key={lane} className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Lane {parseInt(lane) + 1}</span>
                  <span>{count} vehicles</span>
                </div>
                <Progress 
                  value={Math.min(100, (count / 3) * 100)} 
                  className="h-2"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Live Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Live Alerts
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {alerts.length === 0 ? (
              <p className="text-muted-foreground text-center py-4">
                No active alerts
              </p>
            ) : (
              alerts.slice(0, 5).map((alert) => (
                <Alert key={alert.id} className="border-l-4 border-l-red-500">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription className="flex items-center justify-between">
                    <div>
                      <Badge className={`mr-2 ${getSeverityColor(alert.severity)}`}>
                        {alert.severity.toUpperCase()}
                      </Badge>
                      {alert.message}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground">
                      <Clock className="h-3 w-3" />
                      {alert.timestamp.toLocaleTimeString()}
                    </div>
                  </AlertDescription>
                </Alert>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Last Update */}
      <div className="text-center text-sm text-muted-foreground">
        Last updated: {stats.lastUpdate.toLocaleTimeString()}
      </div>
    </div>
  )
} 