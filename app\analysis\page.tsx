import { VideoAnalysisHeader } from "@/components/analysis/video-analysis-header"
import { VideoPlayer } from "@/components/analysis/video-player"
import { SpeedThresholdControls } from "@/components/analysis/speed-threshold-controls"
import { VideoTimeline } from "@/components/analysis/video-timeline"
import { VideoAnalysisStats } from "@/components/analysis/video-analysis-stats"

export default function AnalysisPage() {
  return (
    <div className="container mx-auto p-6">
      <VideoAnalysisHeader />
      <div className="grid gap-6 lg:grid-cols-3 mt-6">
        <div className="lg:col-span-2">
          <VideoPlayer />
          <VideoTimeline className="mt-4" />
        </div>
        <div>
          <SpeedThresholdControls />
          <VideoAnalysisStats className="mt-6" />
        </div>
      </div>
    </div>
  )
}
