"use client"

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface VideoTimelineProps {
  className?: string
}

export function VideoTimeline({ className }: VideoTimelineProps) {
  // Mock event markers for demonstration
  const events = [
    { time: 42, type: "high-speed", position: "10%" },
    { time: 124, type: "high-speed", position: "25%" },
    { time: 187, type: "high-speed", position: "38%" },
    { time: 256, type: "low-speed", position: "52%" },
    { time: 312, type: "high-speed", position: "63%" },
    { time: 378, type: "low-speed", position: "76%" },
    { time: 456, type: "high-speed", position: "92%" },
  ]

  const handleMarkerClick = (time: number) => {
    console.log(`Jumping to time: ${time} seconds`)
  }

  return (
    <Card className={cn(className)}>
      <CardContent className="p-4">
        <div className="relative h-8">
          <div className="absolute top-1/2 left-0 right-0 h-1 bg-muted transform -translate-y-1/2"></div>
          {events.map((event, index) => (
            <button
              key={index}
              className={cn(
                "absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2 w-3 h-3 rounded-full cursor-pointer",
                event.type === "high-speed" ? "bg-red-500" : "bg-blue-500",
              )}
              style={{ left: event.position }}
              onClick={() => handleMarkerClick(event.time)}
              title={`${event.type === "high-speed" ? "High" : "Low"} speed event at ${Math.floor(event.time / 60)}:${(event.time % 60).toString().padStart(2, "0")}`}
            />
          ))}
        </div>
        <div className="flex justify-between mt-2 text-xs text-muted-foreground">
          <span>00:00</span>
          <span>05:00</span>
          <span>10:00</span>
        </div>
      </CardContent>
    </Card>
  )
}
