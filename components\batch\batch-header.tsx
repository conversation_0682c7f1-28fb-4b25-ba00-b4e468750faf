import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, Filter } from "lucide-react"

export function BatchHeader() {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Batch Results</h1>
        <p className="text-muted-foreground">Aggregated analysis results across multiple videos</p>
      </div>
      <div className="flex gap-2">
        <Button variant="outline">
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
        <Button>
          <Download className="mr-2 h-4 w-4" />
          Export Report
        </Button>
      </div>
    </div>
  )
}
