"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Download, Filter } from "lucide-react"
import { videoStore } from "@/lib/video-store"
import { useToast } from "@/hooks/use-toast"

export function BatchHeader() {
  const { toast } = useToast()

  const handleExportReport = () => {
    try {
      const csvData = videoStore.exportResults('csv')
      const blob = new Blob([csvData], { type: 'text/csv' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `traffic_analysis_batch_report_${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

      toast({
        title: "Export successful",
        description: "Batch analysis report has been exported to CSV.",
      })
    } catch (error) {
      toast({
        title: "Export failed",
        description: "Failed to export batch report.",
        variant: "destructive",
      })
    }
  }

  const handleFilter = () => {
    toast({
      title: "Filter feature",
      description: "Advanced filtering options coming soon!",
    })
  }

  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Batch Results</h1>
        <p className="text-muted-foreground">Aggregated analysis results across multiple videos</p>
      </div>
      <div className="flex gap-2">
        <Button variant="outline" onClick={handleFilter}>
          <Filter className="mr-2 h-4 w-4" />
          Filter
        </Button>
        <Button onClick={handleExportReport}>
          <Download className="mr-2 h-4 w-4" />
          Export Report
        </Button>
      </div>
    </div>
  )
}
