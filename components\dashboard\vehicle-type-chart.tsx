"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChartContainer, ChartTooltipContent } from "@/components/ui/chart"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from "recharts"
import { videoStore } from "@/lib/video-store"

export function VehicleTypeChart() {
  const [data, setData] = useState<Array<{name: string, value: number, color: string}>>([])

  useEffect(() => {
    const updateData = () => {
      const stats = videoStore.getStatistics()
      const colors = {
        car: "#2563eb",
        truck: "#16a34a",
        bus: "#ca8a04",
        motorcycle: "#dc2626"
      }

      const chartData = Object.entries(stats.vehicleTypeDistribution).map(([type, count]) => ({
        name: type.charAt(0).toUpperCase() + type.slice(1) + 's',
        value: count,
        color: colors[type as keyof typeof colors] || "#6b7280"
      })).filter(item => item.value > 0)

      setData(chartData)
    }

    updateData()
    const unsubscribe = videoStore.subscribe(updateData)
    return unsubscribe
  }, [])

  if (data.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Vehicle Types</CardTitle>
          <CardDescription>Distribution of detected vehicle types</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center items-center h-[300px]">
          <div className="text-center">
            <p className="text-muted-foreground">No analysis data available</p>
            <p className="text-sm text-muted-foreground mt-1">
              Analyze some videos to see vehicle type distribution
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Vehicle Types</CardTitle>
        <CardDescription>Distribution of detected vehicle types</CardDescription>
      </CardHeader>
      <CardContent className="flex justify-center">
        <ChartContainer
          config={{
            vehicles: {
              label: "Vehicle Types",
              color: "hsl(var(--chart-1))",
            },
          }}
          className="h-[300px] w-full max-w-[300px]"
        >
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={data} cx="50%" cy="50%" labelLine={false} outerRadius={80} fill="#8884d8" dataKey="value">
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<ChartTooltipContent />} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
