"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Chart<PERSON>ontainer, ChartTooltipContent } from "@/components/ui/chart"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON>, Tooltip } from "recharts"

export function VehicleTypeChart() {
  const data = [
    { name: "Cars", value: 65, color: "#2563eb" },
    { name: "Trucks", value: 15, color: "#16a34a" },
    { name: "Buses", value: 10, color: "#ca8a04" },
    { name: "Motorcycles", value: 10, color: "#dc2626" },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>Vehicle Types</CardTitle>
        <CardDescription>Distribution of detected vehicle types</CardDescription>
      </CardHeader>
      <CardContent className="flex justify-center">
        <ChartContainer
          config={{
            vehicles: {
              label: "Vehicle Types",
              color: "hsl(var(--chart-1))",
            },
          }}
          className="h-[300px] w-full max-w-[300px]"
        >
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie data={data} cx="50%" cy="50%" labelLine={false} outerRadius={80} fill="#8884d8" dataKey="value">
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip content={<ChartTooltipContent />} />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        </ChartContainer>
      </CardContent>
    </Card>
  )
}
