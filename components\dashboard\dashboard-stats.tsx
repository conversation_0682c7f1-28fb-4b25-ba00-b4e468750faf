import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Car, AlertTriangle, Clock, Video } from "lucide-react"

export function DashboardStats() {
  const stats = [
    {
      title: "Total Videos",
      value: "24",
      description: "Analyzed videos",
      icon: <Video className="h-4 w-4 text-muted-foreground" />,
      change: "+2 from last week",
    },
    {
      title: "Total Vehicles",
      value: "1,284",
      description: "Detected vehicles",
      icon: <Car className="h-4 w-4 text-muted-foreground" />,
      change: "+120 from last week",
    },
    {
      title: "Speed Violations",
      value: "86",
      description: "High speed vehicles",
      icon: <AlertTriangle className="h-4 w-4 text-red-500" />,
      change: "-4% from last week",
    },
    {
      title: "Avg. Processing Time",
      value: "2.4m",
      description: "Per video",
      icon: <Clock className="h-4 w-4 text-muted-foreground" />,
      change: "Improved by 12%",
    },
  ]

  return (
    <>
      {stats.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
            {stat.icon}
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground mt-1">{stat.change}</p>
          </CardContent>
        </Card>
      ))}
    </>
  )
}
