"use client"

import React, { useState, useCallback } from "react"
import { <PERSON><PERSON>, <PERSON>alogContent, DialogDescription, <PERSON><PERSON>Footer, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Progress } from "@/components/ui/progress"
import { Upload, X, FileVideo, AlertCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface VideoUploadDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onVideoUploaded?: (video: UploadedVideo) => void
}

interface UploadedVideo {
  id: string
  name: string
  size: number
  duration: number
  url: string
  uploadedAt: Date
}

export function VideoUploadDialog({ open, onOpenChange, onVideoUploaded }: VideoUploadDialogProps) {
  const [dragActive, setDragActive] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [error, setError] = useState<string | null>(null)

  const acceptedFormats = ['.mp4', '.avi', '.mov', '.mkv', '.webm']
  const maxFileSize = 500 * 1024 * 1024 // 500MB

  const validateFile = (file: File): string | null => {
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase()
    
    if (!acceptedFormats.includes(fileExtension)) {
      return `Unsupported format. Please use: ${acceptedFormats.join(', ')}`
    }
    
    if (file.size > maxFileSize) {
      return `File too large. Maximum size is ${maxFileSize / (1024 * 1024)}MB`
    }
    
    return null
  }

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    setError(null)

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0]
      const validationError = validateFile(file)
      
      if (validationError) {
        setError(validationError)
        return
      }
      
      setSelectedFile(file)
    }
  }, [])

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    setError(null)
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0]
      const validationError = validateFile(file)
      
      if (validationError) {
        setError(validationError)
        return
      }
      
      setSelectedFile(file)
    }
  }

  const simulateUpload = async (file: File): Promise<UploadedVideo> => {
    return new Promise((resolve) => {
      let progress = 0
      const interval = setInterval(() => {
        progress += Math.random() * 15
        if (progress >= 100) {
          progress = 100
          clearInterval(interval)
          
          // Create object URL for the video
          const url = URL.createObjectURL(file)
          
          // Create video element to get duration
          const video = document.createElement('video')
          video.preload = 'metadata'
          video.onloadedmetadata = () => {
            resolve({
              id: Math.random().toString(36).substr(2, 9),
              name: file.name,
              size: file.size,
              duration: video.duration,
              url: url,
              uploadedAt: new Date()
            })
          }
          video.src = url
        }
        setUploadProgress(progress)
      }, 200)
    })
  }

  const handleUpload = async () => {
    if (!selectedFile) return

    setUploading(true)
    setUploadProgress(0)

    try {
      const uploadedVideo = await simulateUpload(selectedFile)
      onVideoUploaded?.(uploadedVideo)
      
      // Reset state
      setSelectedFile(null)
      setUploadProgress(0)
      setUploading(false)
      onOpenChange(false)
    } catch (error) {
      setError('Upload failed. Please try again.')
      setUploading(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const resetDialog = () => {
    setSelectedFile(null)
    setError(null)
    setUploadProgress(0)
    setUploading(false)
  }

  return (
    <Dialog open={open} onOpenChange={(open) => {
      onOpenChange(open)
      if (!open) resetDialog()
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Video</DialogTitle>
          <DialogDescription>
            Upload a video file for traffic analysis. Supported formats: MP4, AVI, MOV, MKV, WebM
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {!selectedFile && !uploading && (
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
                dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25",
                "hover:border-primary hover:bg-primary/5"
              )}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <div className="space-y-2">
                <p className="text-sm font-medium">Drop your video here, or click to browse</p>
                <p className="text-xs text-muted-foreground">
                  Maximum file size: {maxFileSize / (1024 * 1024)}MB
                </p>
              </div>
              <Input
                type="file"
                accept={acceptedFormats.join(',')}
                onChange={handleFileSelect}
                className="hidden"
                id="video-upload"
              />
              <Label
                htmlFor="video-upload"
                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 mt-4 cursor-pointer"
              >
                Choose File
              </Label>
            </div>
          )}

          {selectedFile && !uploading && (
            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <FileVideo className="h-8 w-8 text-primary" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{selectedFile.name}</p>
                  <p className="text-xs text-muted-foreground">
                    {formatFileSize(selectedFile.size)}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setSelectedFile(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {uploading && (
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <FileVideo className="h-8 w-8 text-primary" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{selectedFile?.name}</p>
                  <p className="text-xs text-muted-foreground">
                    Uploading... {Math.round(uploadProgress)}%
                  </p>
                </div>
              </div>
              <Progress value={uploadProgress} className="w-full" />
            </div>
          )}

          {error && (
            <div className="flex items-center space-x-2 text-destructive text-sm">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={uploading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleUpload}
            disabled={!selectedFile || uploading}
          >
            {uploading ? 'Uploading...' : 'Upload Video'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
