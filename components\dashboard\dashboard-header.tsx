import { But<PERSON> } from "@/components/ui/button"
import { Upload } from "lucide-react"

export function DashboardHeader() {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">Overview of your traffic analysis data</p>
      </div>
      <Button>
        <Upload className="mr-2 h-4 w-4" />
        Upload Video
      </Button>
    </div>
  )
}
